{% extends "base.html" %}

{% block title %}الرئيسية - مزادات إلكترونية{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section bg-primary text-white rounded p-5 mb-5">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="display-4 fw-bold">مرحباً بك في مزادات إلكترونية</h1>
            <p class="lead">منصة موثوقة للمزادات الإلكترونية باللغة العربية. اشترِ وبع بأمان وسهولة.</p>
            <div class="hero-buttons d-flex gap-3 flex-wrap">
                <a href="{{ url_for('live_auctions') }}" class="btn btn-danger btn-lg live-btn">
                    <span class="live-indicator"></span>
                    <i class="fas fa-broadcast-tower me-2"></i>المزادات المباشرة
                </a>
                {% if not current_user.is_authenticated %}
                <a href="{{ url_for('register') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-user-plus me-2"></i>انضم إلينا الآن
                </a>
                {% else %}
                <a href="#auctions" class="btn btn-light btn-lg">
                    <i class="fas fa-search me-2"></i>تصفح المزادات
                </a>
                {% endif %}
            </div>
        </div>
        <div class="col-md-4 text-center">
            <i class="fas fa-gavel fa-5x opacity-75"></i>
        </div>
    </div>
</div>

<!-- Search Section -->
<div class="search-section bg-light rounded p-4 mb-5">
    <h3 class="mb-3"><i class="fas fa-search me-2"></i>البحث في المزادات</h3>
    <form method="GET" action="{{ url_for('index') }}">
        <div class="row g-3">
            <div class="col-md-4">
                {{ search_form.query(class="form-control", placeholder="ابحث عن منتج...") }}
            </div>
            <div class="col-md-3">
                {{ search_form.category_id(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.min_price(class="form-control", placeholder="السعر الأدنى") }}
            </div>
            <div class="col-md-2">
                {{ search_form.max_price(class="form-control", placeholder="السعر الأعلى") }}
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Categories Section -->
<div class="categories-section mb-5">
    <h3 class="mb-4"><i class="fas fa-th-large me-2"></i>الفئات</h3>
    <div class="row">
        {% for category in categories %}
        <div class="col-md-3 col-sm-6 mb-3">
            <a href="{{ url_for('index', category_id=category.id) }}" class="text-decoration-none">
                <div class="card h-100 category-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tag fa-2x text-primary mb-2"></i>
                        <h6 class="card-title">{{ category.name_ar }}</h6>
                        <small class="text-muted">{{ category.name_en }}</small>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Active Auctions Section -->
<div class="auctions-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-fire me-2"></i>المزادات النشطة</h3>
        {% if current_user.is_authenticated and current_user.role.value == 'seller' %}
        <a href="{{ url_for('create_auction') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>إنشاء مزاد جديد
        </a>
        {% endif %}
    </div>

    {% if auctions %}
    <div class="row">
        {% for auction in auctions %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card auction-card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-gavel me-2"></i>{{ auction.title }}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ auction.description[:100] }}{% if auction.description|length > 100 %}...{% endif %}</p>
                    
                    <div class="auction-info">
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">السعر الحالي:</small>
                                <div class="fw-bold text-success">${{ "%.2f"|format(auction.current_price) }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">البائع:</small>
                                <div class="fw-bold">{{ auction.seller.username }}</div>
                            </div>
                        </div>
                        
                        {% if auction.status.value == 'ended' %}
                        <!-- Winner Information for ended auctions -->
                        <div class="winner-info mb-3">
                            <div class="alert alert-warning mb-2">
                                <i class="fas fa-flag-checkered me-1"></i>
                                <strong>انتهى المزاد</strong>
                            </div>
                            {% if auction.winner_id %}
                            <div class="winner-details">
                                <small class="text-muted">الفائز:</small>
                                <div class="fw-bold text-success">
                                    <i class="fas fa-trophy me-1"></i>{{ auction.winner.full_name }}
                                </div>
                                <small class="text-muted">السعر النهائي: ${{ "%.2f"|format(auction.current_price) }}</small>
                            </div>
                            {% else %}
                            <div class="no-winner">
                                <small class="text-muted">
                                    <i class="fas fa-times-circle me-1"></i>لا يوجد فائز (لا توجد مزايدات)
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        {% else %}
                        <!-- Time remaining for active auctions -->
                        <div class="time-remaining mb-3">
                            <small class="text-muted">الوقت المتبقي:</small>
                            <div class="fw-bold text-warning" data-end-time="{{ auction.end_time.isoformat() }}">
                                <i class="fas fa-clock me-1"></i>
                                <span class="countdown-timer"></span>
                            </div>
                        </div>
                        {% endif %}

                        <div class="bid-info">
                            {% if auction.bids %}
                            <small class="text-muted">عدد المزايدات: {{ auction.bids|length }}</small>
                            {% else %}
                            <small class="text-muted">لا توجد مزايدات بعد</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('auction_detail', id=auction.id) }}" class="btn btn-primary w-100">
                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد مزادات نشطة حالياً</h4>
        <p class="text-muted">تحقق مرة أخرى لاحقاً أو قم بإنشاء مزاد جديد</p>
        {% if current_user.is_authenticated and current_user.role.value == 'seller' %}
        <a href="{{ url_for('create_auction') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إنشاء أول مزاد
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Statistics Section -->
<div class="stats-section bg-light rounded p-4 mt-5">
    <div class="row text-center">
        <div class="col-md-3">
            <div class="stat-item">
                <i class="fas fa-gavel fa-2x text-primary mb-2"></i>
                <h4 class="fw-bold">{{ auctions|length }}</h4>
                <p class="text-muted">مزادات نشطة</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-item">
                <i class="fas fa-users fa-2x text-success mb-2"></i>
                <h4 class="fw-bold">1000+</h4>
                <p class="text-muted">مستخدم مسجل</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-item">
                <i class="fas fa-handshake fa-2x text-warning mb-2"></i>
                <h4 class="fw-bold">500+</h4>
                <p class="text-muted">صفقة مكتملة</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-item">
                <i class="fas fa-star fa-2x text-info mb-2"></i>
                <h4 class="fw-bold">4.8/5</h4>
                <p class="text-muted">تقييم المستخدمين</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Countdown timer for auctions
function updateCountdowns() {
    document.querySelectorAll('.countdown-timer').forEach(function(element) {
        const endTime = new Date(element.parentElement.dataset.endTime);
        const now = new Date();
        const timeLeft = endTime - now;
        
        if (timeLeft > 0) {
            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            
            let timeString = '';
            if (days > 0) timeString += days + ' يوم ';
            if (hours > 0) timeString += hours + ' ساعة ';
            timeString += minutes + ' دقيقة';
            
            element.textContent = timeString;
        } else {
            element.textContent = 'انتهى المزاد';
            element.parentElement.classList.remove('text-warning');
            element.parentElement.classList.add('text-danger');
        }
    });
}

// Update countdowns every minute
updateCountdowns();
setInterval(updateCountdowns, 60000);
</script>
{% endblock %}
