<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}مزادات إلكترونية - Electronic Auctions{% endblock %}</title>
    
    <!-- Bootstrap CSS with RTL support -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <!-- Top Bar Styles -->
    <style>
    .top-auction-bar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.5rem 0;
        overflow: hidden;
        position: relative;
        z-index: 1030;
    }

    .auction-ticker {
        display: flex;
        animation: scroll-left var(--scroll-speed, 30s) linear infinite;
        white-space: nowrap;
    }

    .auction-item {
        display: inline-block;
        margin-left: 2rem;
        padding: 8px 20px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 25px;
        text-decoration: none;
        color: white;
        transition: all 0.3s ease;
        min-width: 200px;
        text-align: center;
        font-weight: 500;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .auction-item:hover {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        transform: translateY(-2px);
        cursor: pointer;
    }

    .auction-item .title {
        margin-right: 12px;
        font-size: 14px;
        opacity: 0.95;
    }

    .auction-item .price {
        font-weight: bold;
        color: #ffd700;
        font-size: 16px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        margin: 0 8px;
    }

    .auction-item .status {
        font-size: 12px;
        opacity: 0.8;
        margin-left: 8px;
    }

    @keyframes scroll-left {
        0% { transform: translateX(100%); }
        100% { transform: translateX(-100%); }
    }

    .top-bar-toggle {
        position: absolute;
        top: 50%;
        left: 1rem;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
        cursor: pointer;
        z-index: 1031;
    }

    .top-bar-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .top-auction-bar.collapsed {
        height: 0;
        padding: 0;
        overflow: hidden;
    }

    /* Responsive adjustments for top bar */
    @media (max-width: 768px) {
        .top-auction-bar {
            padding: 10px 0;
            font-size: 13px;
        }

        .auction-item {
            padding: 6px 15px;
            margin-left: 1.5rem;
            min-width: 150px;
        }

        .auction-item .price {
            font-size: 14px;
        }

        .auction-item .title {
            font-size: 12px;
        }

        .auction-item .status {
            font-size: 11px;
        }
    }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Top Auction Bar -->
    <div id="top-auction-bar" class="top-auction-bar">
        <button class="top-bar-toggle" onclick="toggleTopBar()">
            <i class="fas fa-eye-slash" id="toggle-icon"></i>
        </button>
        <div class="auction-ticker" id="auction-ticker">
            <!-- Auctions will be loaded here via JavaScript -->
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-gavel me-2"></i>
                مزادات إلكترونية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link live-nav-link" href="{{ url_for('live_auctions') }}">
                            <span class="live-indicator-small"></span>
                            <i class="fas fa-broadcast-tower me-1"></i>مباشر LIVE
                        </a>
                    </li>
                    {% if current_user.is_authenticated and current_user.role.value == 'seller' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('create_auction') }}">
                            <i class="fas fa-plus me-1"></i>إنشاء مزاد
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            {% if current_user.role.value == 'seller' %}
                            <li><a class="dropdown-item" href="{{ url_for('my_auctions') }}"><i class="fas fa-gavel me-2"></i>مزاداتي</a></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{{ url_for('my_bids') }}"><i class="fas fa-hand-paper me-2"></i>مزايداتي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-bell me-2"></i>الإشعارات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% if current_user.role.value == 'admin' %}
                            <li><a class="dropdown-item" href="{{ url_for('manage_categories') }}"><i class="fas fa-tags me-2"></i>إدارة الفئات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin_settings') }}"><i class="fas fa-cogs me-2"></i>إعدادات النظام</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{{ url_for('edit_profile') }}"><i class="fas fa-edit me-2"></i>تعديل الملف</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('change_password') }}"><i class="fas fa-key me-2"></i>تغيير كلمة المرور</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('register') }}">
                            <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>مزادات إلكترونية</h5>
                    <p>منصة موثوقة للمزادات الإلكترونية باللغة العربية</p>
                </div>
                <div class="col-md-6 text-end">
                    <h5>روابط مفيدة</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">عن الموقع</a></li>
                        <li><a href="#" class="text-light">الشروط والأحكام</a></li>
                        <li><a href="#" class="text-light">اتصل بنا</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 مزادات إلكترونية. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/auction.js') }}"></script>

    <!-- Top Bar JavaScript -->
    <script>
    let topBarEnabled = true;
    let topBarData = null;

    // Load top bar auctions
    async function loadTopBarAuctions() {
        try {
            const response = await fetch('/api/top-bar-auctions');
            const data = await response.json();

            if (data.enabled && data.auctions.length > 0) {
                topBarData = data;
                updateTopBar(data);

                // Update scroll speed
                const ticker = document.getElementById('auction-ticker');
                if (ticker) {
                    ticker.style.setProperty('--scroll-speed', `${data.speed}s`);
                }
            } else {
                hideTopBar();
            }
        } catch (error) {
            console.error('Error loading top bar auctions:', error);
            hideTopBar();
        }
    }

    // Update top bar content
    function updateTopBar(data) {
        const ticker = document.getElementById('auction-ticker');
        if (!ticker) return;

        let html = '';
        data.auctions.forEach(auction => {
            html += `
                <a href="${auction.url}" class="auction-item">
                    <span class="title">${auction.title}</span>
                    <span class="price">${auction.formatted_price}</span>
                    <span class="status">⏰ ${auction.time_remaining}</span>
                </a>
            `;
        });

        // Duplicate content for seamless scrolling
        ticker.innerHTML = html + html;
    }

    // Hide top bar
    function hideTopBar() {
        const topBar = document.getElementById('top-auction-bar');
        if (topBar) {
            topBar.style.display = 'none';
        }
    }

    // Toggle top bar visibility
    function toggleTopBar() {
        const topBar = document.getElementById('top-auction-bar');
        const toggleIcon = document.getElementById('toggle-icon');

        if (topBar.classList.contains('collapsed')) {
            topBar.classList.remove('collapsed');
            toggleIcon.className = 'fas fa-eye-slash';
            localStorage.setItem('topBarVisible', 'true');
        } else {
            topBar.classList.add('collapsed');
            toggleIcon.className = 'fas fa-eye';
            localStorage.setItem('topBarVisible', 'false');
        }
    }

    // Initialize top bar
    document.addEventListener('DOMContentLoaded', function() {
        // Check user preference
        const isVisible = localStorage.getItem('topBarVisible');
        if (isVisible === 'false') {
            const topBar = document.getElementById('top-auction-bar');
            const toggleIcon = document.getElementById('toggle-icon');
            topBar.classList.add('collapsed');
            toggleIcon.className = 'fas fa-eye';
        }

        // Load initial data
        loadTopBarAuctions();

        // Refresh every 30 seconds
        setInterval(loadTopBarAuctions, 30000);
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
