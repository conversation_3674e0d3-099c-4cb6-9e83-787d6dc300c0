/* Arabic Font and RTL Support */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* Override Bootstrap RTL for specific elements */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 15px !important;
}

/* Category Cards */
.category-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 10px;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

/* Auction Cards */
.auction-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.auction-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.auction-card .card-header {
    border-radius: 15px 15px 0 0 !important;
}

/* Auction Info Styling */
.auction-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}

/* Time Remaining Styling */
.time-remaining {
    background: linear-gradient(45deg, #ffc107, #ff8c00);
    color: white;
    padding: 0.5rem;
    border-radius: 8px;
    text-align: center;
}

/* Bid Info */
.bid-info {
    background: #e9ecef;
    padding: 0.5rem;
    border-radius: 5px;
    text-align: center;
}

/* Search Section */
.search-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border: 1px solid #dee2e6;
}

/* Statistics Section */
.stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
}

.stat-item {
    padding: 1rem;
}

.stat-item i {
    display: block;
    margin-bottom: 0.5rem;
}

/* Form Styling */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button Styling */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
}

/* Card Styling */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

/* Navigation Styling */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #ffc107 !important;
}

/* Footer Styling */
footer {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #ffc107 !important;
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

/* Badge Styling */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Table Styling */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
}

/* Countdown Timer */
.countdown-timer {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

/* Bidding Form */
.bidding-form {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

/* Auction Image */
.auction-image {
    border-radius: 10px;
    overflow: hidden;
}

/* Feature Items */
.feature-item {
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .stats-section .row {
        text-align: center;
    }
    
    .auction-card {
        margin-bottom: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Print Styles */
@media print {
    .navbar, .footer, .btn, .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
