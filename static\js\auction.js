// Auction JavaScript Functions

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // Add fade-in animation to cards
    var cards = document.querySelectorAll('.card');
    cards.forEach(function(card, index) {
        setTimeout(function() {
            card.classList.add('fade-in');
        }, index * 100);
    });
});

// Countdown Timer Functions
function formatTime(seconds) {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);
    const secs = seconds % 60;
    
    let result = '';
    if (days > 0) result += days + ' يوم ';
    if (hours > 0) result += hours + ' ساعة ';
    if (minutes > 0) result += minutes + ' دقيقة ';
    result += secs + ' ثانية';
    
    return result;
}

function updateCountdownTimers() {
    const timers = document.querySelectorAll('[data-end-time]');
    
    timers.forEach(function(timer) {
        const endTime = new Date(timer.dataset.endTime);
        const now = new Date();
        const timeLeft = Math.max(0, Math.floor((endTime - now) / 1000));
        
        const timerElement = timer.querySelector('.countdown-timer') || timer;
        
        if (timeLeft > 0) {
            timerElement.textContent = formatTime(timeLeft);
            timer.classList.remove('text-danger');
            timer.classList.add('text-warning');
        } else {
            timerElement.textContent = 'انتهى المزاد';
            timer.classList.remove('text-warning');
            timer.classList.add('text-danger');
            
            // Disable bidding if auction ended
            const biddingForm = document.getElementById('bid-form');
            if (biddingForm) {
                biddingForm.style.display = 'none';
                
                // Show auction ended message
                const endedMessage = document.createElement('div');
                endedMessage.className = 'alert alert-warning text-center';
                endedMessage.innerHTML = '<i class="fas fa-clock me-2"></i>انتهى هذا المزاد';
                biddingForm.parentNode.insertBefore(endedMessage, biddingForm);
            }
        }
    });
}

// Update countdown timers every second
setInterval(updateCountdownTimers, 1000);

// Search functionality
function initializeSearch() {
    const searchForm = document.querySelector('form[action*="index"]');
    if (searchForm) {
        // Add live search functionality
        const searchInput = searchForm.querySelector('input[name="query"]');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    // Auto-submit form after 1 second of no typing
                    if (searchInput.value.length >= 3 || searchInput.value.length === 0) {
                        searchForm.submit();
                    }
                }, 1000);
            });
        }
    }
}

// Bid validation
function validateBidAmount(auctionId, currentPrice) {
    const bidInput = document.getElementById('bid-amount');
    if (bidInput) {
        bidInput.addEventListener('input', function() {
            const bidAmount = parseFloat(this.value);
            const minBid = currentPrice + 1;
            
            if (bidAmount <= currentPrice) {
                this.setCustomValidity('يجب أن تكون المزايدة أكبر من السعر الحالي');
            } else if (bidAmount < minBid) {
                this.setCustomValidity(`الحد الأدنى للمزايدة هو ${minBid.toFixed(2)}`);
            } else {
                this.setCustomValidity('');
            }
        });
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(function() {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Real-time updates using Socket.IO
function initializeSocketIO() {
    if (typeof io !== 'undefined') {
        const socket = io();
        
        // Global socket event listeners
        socket.on('connect', function() {
            console.log('Connected to server');
        });
        
        socket.on('disconnect', function() {
            console.log('Disconnected from server');
        });
        
        socket.on('notification', function(data) {
            showNotification(data.message, data.type || 'info');
        });
        
        return socket;
    }
    return null;
}

// Form submission with loading state
function handleFormSubmission(formId, loadingText = 'جاري المعالجة...') {
    const form = document.getElementById(formId);
    if (form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingText}`;
                submitBtn.disabled = true;
                
                // Re-enable after 5 seconds as fallback
                setTimeout(function() {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }
        });
    }
}

// Image preview functionality
function initializeImagePreview() {
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    
    imageInputs.forEach(function(input) {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create or update preview
                    let preview = document.getElementById('image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.id = 'image-preview';
                        preview.className = 'mt-3';
                        input.parentNode.appendChild(preview);
                    }
                    
                    preview.innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        <p class="text-muted mt-2">${file.name}</p>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    });
}

// Price formatting
function formatPrice(price) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(price);
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Initialize all functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
    initializeImagePreview();
    initializeSmoothScrolling();
    
    // Initialize form handlers
    handleFormSubmission('bid-form', 'جاري تقديم المزايدة...');
    handleFormSubmission('auction-form', 'جاري إنشاء المزاد...');
    
    // Initialize Socket.IO if available
    const socket = initializeSocketIO();
    
    // Start countdown timers
    updateCountdownTimers();
});

// Utility functions
const AuctionUtils = {
    // Format currency
    formatCurrency: function(amount) {
        return '$' + parseFloat(amount).toFixed(2);
    },
    
    // Calculate time remaining
    getTimeRemaining: function(endTime) {
        const now = new Date();
        const end = new Date(endTime);
        return Math.max(0, end - now);
    },
    
    // Check if auction is active
    isAuctionActive: function(startTime, endTime) {
        const now = new Date();
        const start = new Date(startTime);
        const end = new Date(endTime);
        return now >= start && now <= end;
    },
    
    // Generate random auction views (for demo)
    getRandomViews: function() {
        return Math.floor(Math.random() * 200) + 50;
    }
};

// Export for use in other scripts
window.AuctionUtils = AuctionUtils;
