# مزادات إلكترونية - Electronic Auction System

نظام مزادات إلكترونية متكامل باللغة العربية مبني باستخدام Flask و Python.

## المميزات الرئيسية

### 🔐 نظام المستخدمين
- تسجيل حسابات جديدة (بائع/مشتري)
- تسجيل دخول وخروج آمن
- إدارة الملف الشخصي
- أدوار مختلفة للمستخدمين

### 🏷️ إدارة المزادات
- إنشاء مزادات جديدة (للبائعين)
- عرض تفاصيل المزادات
- تصنيف المنتجات
- تحديد مدة المزاد
- السعر الابتدائي والاحتياطي

### 💰 نظام المزايدة
- مزايدة فورية على المنتجات
- تحديثات مباشرة للأسعار
- تاريخ المزايدات
- إشعارات المزايدة

### 🌐 دعم اللغة العربية
- واجهة مستخدم باللغة العربية
- دعم الكتابة من اليمين إلى اليسار (RTL)
- خطوط عربية مناسبة
- ترميز UTF-8 كامل

### ⚡ التحديثات المباشرة
- استخدام Socket.IO للتحديثات الفورية
- عداد تنازلي للوقت المتبقي
- إشعارات فورية للمزايدات الجديدة

## متطلبات النظام

- Python 3.8+
- Flask 2.3+
- SQLite أو PostgreSQL
- متصفح ويب حديث

## التثبيت والتشغيل

### الطريقة السريعة (مستحسنة)
```bash
# 1. تشغيل الإعداد التلقائي
python setup.py

# 2. تشغيل التطبيق
python run.py
```

### الطريقة اليدوية

#### 1. تحميل المشروع
```bash
git clone <repository-url>
cd electronic-auction
```

#### 2. إنشاء بيئة افتراضية (اختياري)
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/Mac
source venv/bin/activate
```

#### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 4. إعداد قاعدة البيانات
```bash
python init_db.py
```

#### 5. تشغيل التطبيق
```bash
python app.py
# أو
python run.py
```

#### 6. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## حسابات تجريبية

بعد تشغيل `init_db.py` ستتوفر الحسابات التالية:

| نوع الحساب | اسم المستخدم | كلمة المرور |
|------------|---------------|-------------|
| مدير | admin | admin123 |
| بائع | ahmed_seller | password123 |
| بائع | fatima_seller | password123 |
| مشتري | omar_buyer | password123 |
| مشتري | sara_buyer | password123 |

## هيكل المشروع

```
electronic-auction/
├── app.py                 # التطبيق الرئيسي
├── models.py             # نماذج قاعدة البيانات
├── forms.py              # نماذج الإدخال
├── config.py             # إعدادات التطبيق
├── init_db.py            # إعداد قاعدة البيانات
├── requirements.txt      # المتطلبات
├── .env                  # متغيرات البيئة
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── login.html
│   ├── register.html
│   ├── create_auction.html
│   └── auction_detail.html
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── auction.js
│   └── images/
└── README.md
```

## الفئات المتاحة

- 📱 إلكترونيات
- 👕 ملابس
- 📚 كتب
- 🚗 سيارات
- 🏠 عقارات
- 🪑 أثاث
- ⚽ رياضة
- 🎨 فن وحرف

## المميزات التقنية

### Backend
- **Flask**: إطار عمل ويب خفيف
- **SQLAlchemy**: ORM لقاعدة البيانات
- **Flask-Login**: إدارة جلسات المستخدمين
- **Flask-SocketIO**: التحديثات المباشرة
- **WTForms**: التحقق من النماذج
- **Werkzeug**: تشفير كلمات المرور

### Frontend
- **Bootstrap 5**: تصميم متجاوب
- **Font Awesome**: الأيقونات
- **Socket.IO**: التحديثات المباشرة
- **JavaScript**: التفاعل والديناميكية
- **CSS3**: تصميم مخصص

### قاعدة البيانات
- **SQLite**: قاعدة بيانات محلية (افتراضي)
- **PostgreSQL**: قاعدة بيانات إنتاج (اختياري)

## الأمان

- تشفير كلمات المرور باستخدام Werkzeug
- حماية CSRF للنماذج
- التحقق من صحة البيانات
- جلسات آمنة
- حماية من SQL Injection

## التطوير المستقبلي

### مميزات مقترحة
- [ ] نظام دفع إلكتروني (PayPal, Stripe)
- [ ] تقييم المستخدمين
- [ ] رفع الصور للمنتجات
- [ ] إشعارات البريد الإلكتروني
- [ ] تطبيق موبايل
- [ ] لوحة تحكم المدير
- [ ] تقارير وإحصائيات
- [ ] دعم عملات متعددة
- [ ] نظام الرسائل بين المستخدمين
- [ ] تصدير البيانات

### تحسينات تقنية
- [ ] Redis للتخزين المؤقت
- [ ] Celery للمهام الخلفية
- [ ] Docker للنشر
- [ ] اختبارات آلية
- [ ] CI/CD Pipeline
- [ ] مراقبة الأداء

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح Issue جديد في GitHub
- راسلنا على البريد الإلكتروني

## شكر خاص

- Bootstrap لإطار العمل الأمامي
- Font Awesome للأيقونات
- Google Fonts للخطوط العربية
- Flask Community للدعم التقني

---

**ملاحظة**: هذا مشروع تعليمي وقد يحتاج إلى تحسينات إضافية للاستخدام في الإنتاج.
