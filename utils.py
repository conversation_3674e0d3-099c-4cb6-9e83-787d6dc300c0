#!/usr/bin/env python3
"""
Utility functions for the Electronic Auction System
Handles image uploads, file management, and other helper functions
"""

import os
import uuid
from PIL import Image
from werkzeug.utils import secure_filename
from flask import current_app
import secrets

# Allowed image extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

# Maximum file size (5MB)
MAX_FILE_SIZE = 5 * 1024 * 1024

# Image dimensions for resizing
THUMBNAIL_SIZE = (300, 300)
MEDIUM_SIZE = (800, 600)
LARGE_SIZE = (1200, 900)

def allowed_file(filename):
    """Check if the file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(filename):
    """Generate a unique filename to prevent conflicts"""
    ext = filename.rsplit('.', 1)[1].lower()
    unique_filename = f"{secrets.token_hex(16)}.{ext}"
    return unique_filename

def create_upload_folder(folder_path):
    """Create upload folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

def save_image(file, upload_folder='static/uploads/auctions', create_thumbnails=True):
    """
    Save uploaded image file and create thumbnails
    
    Args:
        file: FileStorage object from Flask
        upload_folder: Folder to save the image
        create_thumbnails: Whether to create thumbnail versions
    
    Returns:
        dict: Contains original filename and generated filenames
    """
    if not file or not allowed_file(file.filename):
        return None
    
    # Create upload folder if it doesn't exist
    create_upload_folder(upload_folder)
    
    # Generate unique filename
    original_filename = secure_filename(file.filename)
    unique_filename = generate_unique_filename(original_filename)
    
    # Full path for saving
    file_path = os.path.join(upload_folder, unique_filename)
    
    try:
        # Save original file
        file.save(file_path)
        
        result = {
            'original_filename': original_filename,
            'saved_filename': unique_filename,
            'file_path': file_path,
            'url': f"/{file_path}",
            'thumbnails': {}
        }
        
        if create_thumbnails:
            # Create thumbnails using PIL
            try:
                with Image.open(file_path) as img:
                    # Convert to RGB if necessary (for PNG with transparency)
                    if img.mode in ('RGBA', 'LA', 'P'):
                        img = img.convert('RGB')
                    
                    # Create thumbnail
                    thumb_filename = f"thumb_{unique_filename}"
                    thumb_path = os.path.join(upload_folder, thumb_filename)
                    
                    # Create thumbnail copy
                    thumb_img = img.copy()
                    thumb_img.thumbnail(THUMBNAIL_SIZE, Image.Resampling.LANCZOS)
                    thumb_img.save(thumb_path, 'JPEG', quality=85)
                    
                    result['thumbnails']['thumb'] = {
                        'filename': thumb_filename,
                        'path': thumb_path,
                        'url': f"/{thumb_path}",
                        'size': THUMBNAIL_SIZE
                    }
                    
                    # Create medium size
                    medium_filename = f"medium_{unique_filename}"
                    medium_path = os.path.join(upload_folder, medium_filename)
                    
                    medium_img = img.copy()
                    medium_img.thumbnail(MEDIUM_SIZE, Image.Resampling.LANCZOS)
                    medium_img.save(medium_path, 'JPEG', quality=90)
                    
                    result['thumbnails']['medium'] = {
                        'filename': medium_filename,
                        'path': medium_path,
                        'url': f"/{medium_path}",
                        'size': MEDIUM_SIZE
                    }
                    
            except Exception as e:
                print(f"Error creating thumbnails: {e}")
                # Continue without thumbnails if there's an error
        
        return result
        
    except Exception as e:
        print(f"Error saving image: {e}")
        return None

def delete_image_files(filename, upload_folder='static/uploads/auctions'):
    """Delete image file and its thumbnails"""
    try:
        # Delete original file
        original_path = os.path.join(upload_folder, filename)
        if os.path.exists(original_path):
            os.remove(original_path)
        
        # Delete thumbnails
        thumb_path = os.path.join(upload_folder, f"thumb_{filename}")
        if os.path.exists(thumb_path):
            os.remove(thumb_path)
            
        medium_path = os.path.join(upload_folder, f"medium_{filename}")
        if os.path.exists(medium_path):
            os.remove(medium_path)
            
        return True
    except Exception as e:
        print(f"Error deleting image files: {e}")
        return False

def get_image_url(filename, size='original', upload_folder='static/uploads/auctions'):
    """Get URL for image based on size"""
    if not filename:
        return None
    
    if size == 'thumb':
        return f"/{upload_folder}/thumb_{filename}"
    elif size == 'medium':
        return f"/{upload_folder}/medium_{filename}"
    else:
        return f"/{upload_folder}/{filename}"

def validate_image_file(file):
    """Validate uploaded image file"""
    errors = []
    
    if not file:
        errors.append("لم يتم اختيار ملف / No file selected")
        return errors
    
    if not file.filename:
        errors.append("اسم الملف فارغ / Empty filename")
        return errors
    
    if not allowed_file(file.filename):
        errors.append("نوع الملف غير مدعوم. يُسمح فقط بـ JPG, PNG, GIF / Unsupported file type. Only JPG, PNG, GIF allowed")
    
    # Check file size (this is approximate, actual size check happens during upload)
    file.seek(0, 2)  # Seek to end
    file_size = file.tell()
    file.seek(0)  # Reset to beginning
    
    if file_size > MAX_FILE_SIZE:
        errors.append(f"حجم الملف كبير جداً. الحد الأقصى {MAX_FILE_SIZE // (1024*1024)}MB / File too large. Maximum {MAX_FILE_SIZE // (1024*1024)}MB")
    
    return errors

def process_auction_images(image_files, auction_id):
    """Process multiple images for an auction"""
    saved_images = []
    errors = []
    
    if not image_files:
        return saved_images, errors
    
    for i, file in enumerate(image_files):
        if file and file.filename:
            # Validate file
            file_errors = validate_image_file(file)
            if file_errors:
                errors.extend([f"صورة {i+1}: {error}" for error in file_errors])
                continue
            
            # Save image
            result = save_image(file)
            if result:
                saved_images.append({
                    'filename': result['saved_filename'],
                    'original_name': result['original_filename'],
                    'url': result['url'],
                    'thumbnails': result['thumbnails'],
                    'is_primary': i == 0  # First image is primary
                })
            else:
                errors.append(f"فشل في حفظ الصورة {i+1} / Failed to save image {i+1}")
    
    return saved_images, errors

def get_auction_images(auction):
    """Get all images for an auction with URLs"""
    images = []
    for img in auction.images:
        images.append({
            'id': img.id,
            'filename': img.filename,
            'is_primary': img.is_primary,
            'original_url': get_image_url(img.filename, 'original'),
            'medium_url': get_image_url(img.filename, 'medium'),
            'thumb_url': get_image_url(img.filename, 'thumb')
        })
    return images
