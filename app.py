from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_migrate import Migrate
from datetime import datetime, timedelta
import os

from config import Config
from models import db, User, Auction, Bid, Category, AuctionStatus, UserRole, Notification
from forms import LoginForm, RegistrationForm, AuctionForm, BidForm, SearchForm

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة / Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Initialize SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # Routes
    @app.route('/')
    def index():
        search_form = SearchForm()

        # Get active auctions
        query = Auction.query.filter_by(status=AuctionStatus.ACTIVE)

        # Apply search filters
        if request.args.get('query'):
            search_term = request.args.get('query')
            query = query.filter(Auction.title.contains(search_term))

        if request.args.get('category_id'):
            try:
                category_id = int(request.args.get('category_id'))
                query = query.filter_by(category_id=category_id)
            except (ValueError, TypeError):
                pass  # Ignore invalid category_id

        auctions = query.order_by(Auction.end_time.asc()).limit(12).all()
        categories = Category.query.all()

        return render_template('index.html', auctions=auctions, categories=categories, search_form=search_form)
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if current_user.is_authenticated:
            return redirect(url_for('index'))
        
        form = LoginForm()
        if form.validate_on_submit():
            user = User.query.filter_by(username=form.username.data).first()
            if user and user.check_password(form.password.data):
                login_user(user, remember=form.remember_me.data)
                next_page = request.args.get('next')
                flash('تم تسجيل الدخول بنجاح / Login successful!', 'success')
                return redirect(next_page) if next_page else redirect(url_for('index'))
            flash('اسم المستخدم أو كلمة المرور غير صحيحة / Invalid username or password', 'danger')
        
        return render_template('login.html', form=form)
    
    @app.route('/register', methods=['GET', 'POST'])
    def register():
        if current_user.is_authenticated:
            return redirect(url_for('index'))
        
        form = RegistrationForm()
        if form.validate_on_submit():
            user = User(
                username=form.username.data,
                email=form.email.data,
                full_name=form.full_name.data,
                phone=form.phone.data,
                role=UserRole(form.role.data)
            )
            user.set_password(form.password.data)
            db.session.add(user)
            db.session.commit()
            
            flash('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول / Account created successfully! You can now log in.', 'success')
            return redirect(url_for('login'))
        
        return render_template('register.html', form=form)
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح / Logged out successfully', 'info')
        return redirect(url_for('index'))
    
    @app.route('/create_auction', methods=['GET', 'POST'])
    @login_required
    def create_auction():
        if current_user.role != UserRole.SELLER:
            flash('يجب أن تكون بائعاً لإنشاء مزاد / You must be a seller to create an auction', 'danger')
            return redirect(url_for('index'))
        
        form = AuctionForm()
        if form.validate_on_submit():
            end_time = datetime.utcnow() + timedelta(days=form.duration_days.data)
            
            auction = Auction(
                title=form.title.data,
                description=form.description.data,
                starting_price=form.starting_price.data,
                current_price=form.starting_price.data,
                reserve_price=form.reserve_price.data,
                end_time=end_time,
                seller_id=current_user.id,
                category_id=form.category_id.data,
                status=AuctionStatus.ACTIVE
            )
            
            db.session.add(auction)
            db.session.commit()
            
            flash('تم إنشاء المزاد بنجاح! / Auction created successfully!', 'success')
            return redirect(url_for('auction_detail', id=auction.id))
        
        return render_template('create_auction.html', form=form)
    
    @app.route('/auction/<int:id>')
    def auction_detail(id):
        auction = Auction.query.get_or_404(id)
        bid_form = BidForm(auction=auction)
        
        # Get recent bids
        recent_bids = Bid.query.filter_by(auction_id=id).order_by(Bid.timestamp.desc()).limit(10).all()
        
        return render_template('auction_detail.html', auction=auction, bid_form=bid_form, recent_bids=recent_bids)
    
    @app.route('/place_bid/<int:auction_id>', methods=['POST'])
    @login_required
    def place_bid(auction_id):
        auction = Auction.query.get_or_404(auction_id)
        
        if not auction.is_active:
            return jsonify({'success': False, 'message': 'المزاد غير نشط / Auction is not active'})
        
        if auction.seller_id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك المزايدة على مزادك الخاص / You cannot bid on your own auction'})
        
        form = BidForm(auction=auction)
        if form.validate_on_submit():
            bid = Bid(
                amount=form.amount.data,
                auction_id=auction_id,
                bidder_id=current_user.id
            )
            
            # Update auction current price
            auction.current_price = form.amount.data
            
            db.session.add(bid)
            db.session.commit()
            
            # Emit real-time update
            socketio.emit('new_bid', {
                'auction_id': auction_id,
                'amount': form.amount.data,
                'bidder': current_user.username,
                'timestamp': bid.timestamp.strftime('%Y-%m-%d %H:%M:%S')
            }, room=f'auction_{auction_id}')
            
            return jsonify({'success': True, 'message': 'تم تقديم المزايدة بنجاح / Bid placed successfully'})
        
        return jsonify({'success': False, 'message': 'خطأ في البيانات / Invalid data'})
    
    # SocketIO events
    @socketio.on('join_auction')
    def on_join_auction(data):
        auction_id = data['auction_id']
        join_room(f'auction_{auction_id}')
        emit('status', {'msg': f'Joined auction {auction_id}'})
    
    @socketio.on('leave_auction')
    def on_leave_auction(data):
        auction_id = data['auction_id']
        leave_room(f'auction_{auction_id}')
        emit('status', {'msg': f'Left auction {auction_id}'})
    
    # Initialize database
    with app.app_context():
        db.create_all()

        # Create default categories if they don't exist
        if Category.query.count() == 0:
            categories = [
                Category(name_ar='إلكترونيات', name_en='Electronics'),
                Category(name_ar='ملابس', name_en='Clothing'),
                Category(name_ar='كتب', name_en='Books'),
                Category(name_ar='سيارات', name_en='Cars'),
                Category(name_ar='عقارات', name_en='Real Estate'),
                Category(name_ar='أثاث', name_en='Furniture'),
                Category(name_ar='رياضة', name_en='Sports'),
                Category(name_ar='فن وحرف', name_en='Art & Crafts'),
            ]

            for category in categories:
                db.session.add(category)

            db.session.commit()
    
    return app, socketio

if __name__ == '__main__':
    app, socketio = create_app()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
