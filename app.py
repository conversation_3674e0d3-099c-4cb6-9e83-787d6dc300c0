from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_login import <PERSON>gin<PERSON>ana<PERSON>, login_user, logout_user, login_required, current_user
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_migrate import Migrate
from datetime import datetime, timedelta
import os

from config import Config
from models import db, User, Auction, Bid, Category, AuctionStatus, UserRole, Notification, AuctionImage
from forms import LoginForm, RegistrationForm, AuctionForm, BidForm, SearchForm, ProfileForm, ChangePasswordForm
from utils import process_auction_images, get_auction_images

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة / Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Initialize SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # Routes
    @app.route('/')
    def index():
        search_form = SearchForm()

        # Get active auctions
        query = Auction.query.filter_by(status=AuctionStatus.ACTIVE)

        # Apply search filters
        if request.args.get('query'):
            search_term = request.args.get('query')
            query = query.filter(Auction.title.contains(search_term))

        if request.args.get('category_id'):
            try:
                category_id = int(request.args.get('category_id'))
                query = query.filter_by(category_id=category_id)
            except (ValueError, TypeError):
                pass  # Ignore invalid category_id

        auctions = query.order_by(Auction.end_time.asc()).limit(12).all()
        categories = Category.query.all()

        return render_template('index.html', auctions=auctions, categories=categories, search_form=search_form)
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if current_user.is_authenticated:
            return redirect(url_for('index'))
        
        form = LoginForm()
        if form.validate_on_submit():
            user = User.query.filter_by(username=form.username.data).first()
            if user and user.check_password(form.password.data):
                login_user(user, remember=form.remember_me.data)
                next_page = request.args.get('next')
                flash('تم تسجيل الدخول بنجاح / Login successful!', 'success')
                return redirect(next_page) if next_page else redirect(url_for('index'))
            flash('اسم المستخدم أو كلمة المرور غير صحيحة / Invalid username or password', 'danger')
        
        return render_template('login.html', form=form)
    
    @app.route('/register', methods=['GET', 'POST'])
    def register():
        if current_user.is_authenticated:
            return redirect(url_for('index'))
        
        form = RegistrationForm()
        if form.validate_on_submit():
            user = User(
                username=form.username.data,
                email=form.email.data,
                full_name=form.full_name.data,
                phone=form.phone.data,
                role=UserRole(form.role.data)
            )
            user.set_password(form.password.data)
            db.session.add(user)
            db.session.commit()
            
            flash('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول / Account created successfully! You can now log in.', 'success')
            return redirect(url_for('login'))
        
        return render_template('register.html', form=form)
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح / Logged out successfully', 'info')
        return redirect(url_for('index'))
    
    @app.route('/create_auction', methods=['GET', 'POST'])
    @login_required
    def create_auction():
        if current_user.role != UserRole.SELLER:
            flash('يجب أن تكون بائعاً لإنشاء مزاد / You must be a seller to create an auction', 'danger')
            return redirect(url_for('index'))

        form = AuctionForm()
        if form.validate_on_submit():
            end_time = datetime.utcnow() + timedelta(days=form.duration_days.data)

            # Create auction first
            auction = Auction(
                title=form.title.data,
                description=form.description.data,
                starting_price=form.starting_price.data,
                current_price=form.starting_price.data,
                reserve_price=form.reserve_price.data,
                end_time=end_time,
                seller_id=current_user.id,
                category_id=form.category_id.data,
                status=AuctionStatus.ACTIVE
            )

            db.session.add(auction)
            db.session.commit()  # Commit to get auction.id

            # Process uploaded images
            if form.images.data:
                saved_images, image_errors = process_auction_images(form.images.data, auction.id)

                if image_errors:
                    for error in image_errors:
                        flash(error, 'warning')

                # Save image records to database
                for img_data in saved_images:
                    auction_image = AuctionImage(
                        filename=img_data['filename'],
                        is_primary=img_data['is_primary'],
                        auction_id=auction.id
                    )
                    db.session.add(auction_image)

                db.session.commit()

                if saved_images:
                    flash(f'تم رفع {len(saved_images)} صورة بنجاح / {len(saved_images)} images uploaded successfully', 'success')

            flash('تم إنشاء المزاد بنجاح! / Auction created successfully!', 'success')
            return redirect(url_for('auction_detail', id=auction.id))

        return render_template('create_auction.html', form=form)
    
    @app.route('/auction/<int:id>')
    def auction_detail(id):
        auction = Auction.query.get_or_404(id)
        bid_form = BidForm(auction=auction)

        # Get recent bids
        recent_bids = Bid.query.filter_by(auction_id=id).order_by(Bid.timestamp.desc()).limit(10).all()

        # Get auction images
        auction_images = get_auction_images(auction)

        return render_template('auction_detail.html', auction=auction, bid_form=bid_form,
                             recent_bids=recent_bids, auction_images=auction_images)
    
    @app.route('/place_bid/<int:auction_id>', methods=['POST'])
    @login_required
    def place_bid(auction_id):
        auction = Auction.query.get_or_404(auction_id)
        
        if not auction.is_active:
            return jsonify({'success': False, 'message': 'المزاد غير نشط / Auction is not active'})
        
        if auction.seller_id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك المزايدة على مزادك الخاص / You cannot bid on your own auction'})
        
        form = BidForm(auction=auction)
        if form.validate_on_submit():
            bid = Bid(
                amount=form.amount.data,
                auction_id=auction_id,
                bidder_id=current_user.id
            )
            
            # Update auction current price
            auction.current_price = form.amount.data
            
            db.session.add(bid)
            db.session.commit()
            
            # Emit real-time update
            socketio.emit('new_bid', {
                'auction_id': auction_id,
                'amount': form.amount.data,
                'bidder': current_user.username,
                'timestamp': bid.timestamp.strftime('%Y-%m-%d %H:%M:%S')
            }, room=f'auction_{auction_id}')
            
            return jsonify({'success': True, 'message': 'تم تقديم المزايدة بنجاح / Bid placed successfully'})
        
        return jsonify({'success': False, 'message': 'خطأ في البيانات / Invalid data'})

    @app.route('/profile')
    @login_required
    def profile():
        # Get user's auctions
        user_auctions = Auction.query.filter_by(seller_id=current_user.id).order_by(Auction.created_at.desc()).all()

        # Get user's bids
        user_bids = Bid.query.filter_by(bidder_id=current_user.id).order_by(Bid.timestamp.desc()).limit(10).all()

        # Get user's won auctions
        won_auctions = Auction.query.filter_by(winner_id=current_user.id).all()

        # Calculate statistics
        stats = {
            'total_auctions': len(user_auctions),
            'active_auctions': len([a for a in user_auctions if a.status == AuctionStatus.ACTIVE]),
            'total_bids': len(user_bids),
            'won_auctions': len(won_auctions),
            'total_sales': sum([a.current_price for a in user_auctions if a.status == AuctionStatus.ENDED and a.winner_id]),
        }

        return render_template('profile.html',
                             user_auctions=user_auctions,
                             user_bids=user_bids,
                             won_auctions=won_auctions,
                             stats=stats)

    @app.route('/edit_profile', methods=['GET', 'POST'])
    @login_required
    def edit_profile():
        form = ProfileForm(current_user.email)

        if form.validate_on_submit():
            current_user.full_name = form.full_name.data
            current_user.email = form.email.data
            current_user.phone = form.phone.data

            db.session.commit()
            flash('تم تحديث الملف الشخصي بنجاح / Profile updated successfully!', 'success')
            return redirect(url_for('profile'))

        # Pre-populate form with current user data
        if request.method == 'GET':
            form.full_name.data = current_user.full_name
            form.email.data = current_user.email
            form.phone.data = current_user.phone

        return render_template('edit_profile.html', form=form)

    @app.route('/change_password', methods=['GET', 'POST'])
    @login_required
    def change_password():
        form = ChangePasswordForm()

        if form.validate_on_submit():
            if current_user.check_password(form.current_password.data):
                current_user.set_password(form.new_password.data)
                db.session.commit()
                flash('تم تغيير كلمة المرور بنجاح / Password changed successfully!', 'success')
                return redirect(url_for('profile'))
            else:
                flash('كلمة المرور الحالية غير صحيحة / Current password is incorrect', 'danger')

        return render_template('change_password.html', form=form)

    @app.route('/my_auctions')
    @login_required
    def my_auctions():
        if current_user.role != UserRole.SELLER:
            flash('هذه الصفحة متاحة للبائعين فقط / This page is for sellers only', 'warning')
            return redirect(url_for('profile'))

        auctions = Auction.query.filter_by(seller_id=current_user.id).order_by(Auction.created_at.desc()).all()
        return render_template('my_auctions.html', auctions=auctions)

    @app.route('/my_bids')
    @login_required
    def my_bids():
        bids = Bid.query.filter_by(bidder_id=current_user.id).order_by(Bid.timestamp.desc()).all()
        return render_template('my_bids.html', bids=bids)

    # SocketIO events
    @socketio.on('join_auction')
    def on_join_auction(data):
        auction_id = data['auction_id']
        room = f'auction_{auction_id}'
        join_room(room)

        # Get auction info
        auction = Auction.query.get(auction_id)
        if auction:
            # Send current auction state to the new user
            emit('auction_state', {
                'auction_id': auction_id,
                'current_price': float(auction.current_price),
                'total_bids': len(auction.bids),
                'time_remaining': (auction.end_time - datetime.utcnow()).total_seconds() if auction.is_active else 0,
                'status': auction.status.value
            })

            # Notify others about new viewer
            emit('user_joined', {
                'auction_id': auction_id,
                'username': current_user.username if current_user.is_authenticated else 'زائر',
                'user_count': len(socketio.server.manager.rooms.get('/', {}).get(room, {}))
            }, room=room, include_self=False)

            print(f"User {current_user.username if current_user.is_authenticated else 'Anonymous'} joined auction {auction_id}")

    @socketio.on('leave_auction')
    def on_leave_auction(data):
        auction_id = data['auction_id']
        room = f'auction_{auction_id}'
        leave_room(room)

        # Notify others about user leaving
        emit('user_left', {
            'auction_id': auction_id,
            'username': current_user.username if current_user.is_authenticated else 'زائر',
            'user_count': len(socketio.server.manager.rooms.get('/', {}).get(room, {})) - 1
        }, room=room)

        print(f"User {current_user.username if current_user.is_authenticated else 'Anonymous'} left auction {auction_id}")

    @socketio.on('place_bid_realtime')
    def on_place_bid_realtime(data):
        if not current_user.is_authenticated:
            emit('bid_error', {'message': 'يجب تسجيل الدخول للمزايدة / Please login to bid'})
            return

        auction_id = data['auction_id']
        bid_amount = float(data['amount'])

        auction = Auction.query.get(auction_id)
        if not auction:
            emit('bid_error', {'message': 'المزاد غير موجود / Auction not found'})
            return

        if not auction.is_active:
            emit('bid_error', {'message': 'المزاد غير نشط / Auction is not active'})
            return

        if auction.seller_id == current_user.id:
            emit('bid_error', {'message': 'لا يمكنك المزايدة على مزادك الخاص / Cannot bid on your own auction'})
            return

        if bid_amount <= auction.current_price:
            emit('bid_error', {'message': f'يجب أن تكون المزايدة أكبر من {auction.current_price} / Bid must be higher than {auction.current_price}'})
            return

        # Create new bid
        bid = Bid(
            amount=bid_amount,
            auction_id=auction_id,
            bidder_id=current_user.id
        )

        # Update auction current price
        auction.current_price = bid_amount

        db.session.add(bid)
        db.session.commit()

        # Emit real-time update to all users in the auction room
        room = f'auction_{auction_id}'
        socketio.emit('new_bid', {
            'auction_id': auction_id,
            'amount': float(bid_amount),
            'bidder_name': current_user.full_name,
            'bidder_username': current_user.username,
            'timestamp': bid.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'total_bids': len(auction.bids),
            'time_remaining': (auction.end_time - datetime.utcnow()).total_seconds() if auction.is_active else 0
        }, room=room)

        # Send success confirmation to bidder
        emit('bid_success', {
            'message': 'تم تقديم المزايدة بنجاح / Bid placed successfully',
            'amount': float(bid_amount)
        })

        print(f"New bid: {bid_amount} by {current_user.username} on auction {auction_id}")

    @socketio.on('get_auction_stats')
    def on_get_auction_stats(data):
        auction_id = data['auction_id']
        auction = Auction.query.get(auction_id)

        if auction:
            recent_bids = Bid.query.filter_by(auction_id=auction_id).order_by(Bid.timestamp.desc()).limit(5).all()

            emit('auction_stats', {
                'auction_id': auction_id,
                'current_price': float(auction.current_price),
                'starting_price': float(auction.starting_price),
                'total_bids': len(auction.bids),
                'time_remaining': (auction.end_time - datetime.utcnow()).total_seconds() if auction.is_active else 0,
                'recent_bids': [{
                    'amount': float(bid.amount),
                    'bidder': bid.bidder.full_name,
                    'timestamp': bid.timestamp.strftime('%H:%M:%S')
                } for bid in recent_bids],
                'status': auction.status.value
            })

    @socketio.on('connect')
    def on_connect():
        print(f"User connected: {current_user.username if current_user.is_authenticated else 'Anonymous'}")

    @socketio.on('disconnect')
    def on_disconnect():
        print(f"User disconnected: {current_user.username if current_user.is_authenticated else 'Anonymous'}")
    
    # Initialize database
    with app.app_context():
        db.create_all()

        # Create default categories if they don't exist
        if Category.query.count() == 0:
            categories = [
                Category(name_ar='إلكترونيات', name_en='Electronics'),
                Category(name_ar='ملابس', name_en='Clothing'),
                Category(name_ar='كتب', name_en='Books'),
                Category(name_ar='سيارات', name_en='Cars'),
                Category(name_ar='عقارات', name_en='Real Estate'),
                Category(name_ar='أثاث', name_en='Furniture'),
                Category(name_ar='رياضة', name_en='Sports'),
                Category(name_ar='فن وحرف', name_en='Art & Crafts'),
            ]

            for category in categories:
                db.session.add(category)

            db.session.commit()
    
    return app, socketio

if __name__ == '__main__':
    app, socketio = create_app()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
