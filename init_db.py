#!/usr/bin/env python3
"""
Database initialization script for the Electronic Auction System
This script creates the database tables and populates them with sample data
"""

from app import create_app
from models import db, User, Category, Auction, Bid, UserRole, AuctionStatus
from datetime import datetime, timedelta
import random

def init_database():
    """Initialize the database with tables and sample data"""
    app, socketio = create_app()
    
    with app.app_context():
        # Drop all tables and recreate them
        print("Creating database tables...")
        db.drop_all()
        db.create_all()
        
        # Create categories
        print("Creating categories...")
        categories = [
            Category(name_ar='إلكترونيات', name_en='Electronics', 
                    description_ar='أجهزة إلكترونية وتقنية', description_en='Electronic devices and technology'),
            Category(name_ar='ملابس', name_en='Clothing', 
                    description_ar='ملابس وأزياء', description_en='Clothes and fashion'),
            Category(name_ar='كتب', name_en='Books', 
                    description_ar='كتب ومراجع', description_en='Books and references'),
            Category(name_ar='سيارات', name_en='Cars', 
                    description_ar='سيارات ومركبات', description_en='Cars and vehicles'),
            Category(name_ar='عقارات', name_en='Real Estate', 
                    description_ar='عقارات وأراضي', description_en='Real estate and land'),
            Category(name_ar='أثاث', name_en='Furniture', 
                    description_ar='أثاث منزلي', description_en='Home furniture'),
            Category(name_ar='رياضة', name_en='Sports', 
                    description_ar='معدات رياضية', description_en='Sports equipment'),
            Category(name_ar='فن وحرف', name_en='Art & Crafts', 
                    description_ar='أعمال فنية وحرفية', description_en='Art and craft works'),
        ]
        
        for category in categories:
            db.session.add(category)
        
        # Create sample users
        print("Creating sample users...")
        users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'full_name': 'مدير النظام',
                'phone': '+966501234567',
                'role': UserRole.ADMIN,
                'password': 'admin123'
            },
            {
                'username': 'ahmed_seller',
                'email': '<EMAIL>',
                'full_name': 'أحمد محمد',
                'phone': '+966501234568',
                'role': UserRole.SELLER,
                'password': 'password123'
            },
            {
                'username': 'fatima_seller',
                'email': '<EMAIL>',
                'full_name': 'فاطمة علي',
                'phone': '+966501234569',
                'role': UserRole.SELLER,
                'password': 'password123'
            },
            {
                'username': 'omar_buyer',
                'email': '<EMAIL>',
                'full_name': 'عمر خالد',
                'phone': '+966501234570',
                'role': UserRole.BUYER,
                'password': 'password123'
            },
            {
                'username': 'sara_buyer',
                'email': '<EMAIL>',
                'full_name': 'سارة أحمد',
                'phone': '+966501234571',
                'role': UserRole.BUYER,
                'password': 'password123'
            }
        ]
        
        user_objects = []
        for user_data in users:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                full_name=user_data['full_name'],
                phone=user_data['phone'],
                role=user_data['role']
            )
            user.set_password(user_data['password'])
            db.session.add(user)
            user_objects.append(user)
        
        # Commit users and categories first
        db.session.commit()
        
        # Create sample auctions
        print("Creating sample auctions...")
        auction_data = [
            {
                'title': 'آيفون 14 برو مستعمل بحالة ممتازة',
                'description': 'آيفون 14 برو 256 جيجا، لون أزرق، مستعمل لمدة 6 أشهر فقط. الجهاز بحالة ممتازة مع جميع الملحقات الأصلية.',
                'starting_price': 800.0,
                'category_id': 1,  # Electronics
                'seller_id': 2,    # ahmed_seller
                'duration_days': 7
            },
            {
                'title': 'ساعة ذكية سامسونج جالاكسي واتش',
                'description': 'ساعة ذكية سامسونج جالاكسي واتش 5، مقاس 44 ملم، لون أسود. تتضمن جميع المميزات الصحية والرياضية.',
                'starting_price': 200.0,
                'category_id': 1,  # Electronics
                'seller_id': 3,    # fatima_seller
                'duration_days': 5
            },
            {
                'title': 'كتاب البرمجة بلغة Python',
                'description': 'كتاب شامل لتعلم البرمجة بلغة Python من البداية حتى الاحتراف. نسخة أصلية باللغة العربية.',
                'starting_price': 25.0,
                'category_id': 3,  # Books
                'seller_id': 2,    # ahmed_seller
                'duration_days': 3
            },
            {
                'title': 'طاولة مكتب خشبية فاخرة',
                'description': 'طاولة مكتب من الخشب الطبيعي، تصميم عصري وأنيق. مناسبة للمكاتب والمنازل.',
                'starting_price': 150.0,
                'category_id': 6,  # Furniture
                'seller_id': 3,    # fatima_seller
                'duration_days': 10
            },
            {
                'title': 'دراجة هوائية جبلية',
                'description': 'دراجة هوائية جبلية عالية الجودة، مناسبة للطرق الوعرة والمغامرات. حالة ممتازة.',
                'starting_price': 300.0,
                'category_id': 7,  # Sports
                'seller_id': 2,    # ahmed_seller
                'duration_days': 14
            }
        ]
        
        auction_objects = []
        for auction_info in auction_data:
            end_time = datetime.utcnow() + timedelta(days=auction_info['duration_days'])
            
            auction = Auction(
                title=auction_info['title'],
                description=auction_info['description'],
                starting_price=auction_info['starting_price'],
                current_price=auction_info['starting_price'],
                reserve_price=auction_info['starting_price'] * 1.5,  # 50% above starting price
                end_time=end_time,
                seller_id=auction_info['seller_id'],
                category_id=auction_info['category_id'],
                status=AuctionStatus.ACTIVE
            )
            
            db.session.add(auction)
            auction_objects.append(auction)
        
        # Commit auctions
        db.session.commit()
        
        # Create sample bids
        print("Creating sample bids...")
        for auction in auction_objects:
            # Add 2-5 random bids per auction
            num_bids = random.randint(2, 5)
            current_price = auction.starting_price
            
            for i in range(num_bids):
                # Random bidder (buyers only)
                bidder_id = random.choice([4, 5])  # omar_buyer or sara_buyer
                
                # Increment price by 5-20%
                increment = current_price * random.uniform(0.05, 0.20)
                current_price += increment
                
                bid = Bid(
                    amount=round(current_price, 2),
                    auction_id=auction.id,
                    bidder_id=bidder_id,
                    timestamp=datetime.utcnow() - timedelta(hours=random.randint(1, 48))
                )
                
                db.session.add(bid)
            
            # Update auction current price
            auction.current_price = round(current_price, 2)
        
        # Final commit
        db.session.commit()
        
        print("Database initialized successfully!")
        print("\nSample user accounts:")
        print("Admin: admin / admin123")
        print("Seller: ahmed_seller / password123")
        print("Seller: fatima_seller / password123")
        print("Buyer: omar_buyer / password123")
        print("Buyer: sara_buyer / password123")

if __name__ == '__main__':
    init_database()
