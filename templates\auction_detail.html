{% extends "base.html" %}

{% block title %}{{ auction.title }} - مزادات إلكترونية{% endblock %}

{% block content %}
<div class="row">
    <!-- Auction Details -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ auction.title }}</h4>
            </div>
            <div class="card-body">
                <!-- Auction Image Placeholder -->
                <div class="auction-image mb-4">
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                        <i class="fas fa-image fa-5x text-muted"></i>
                    </div>
                </div>
                
                <!-- Auction Info -->
                <div class="auction-info">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-muted">الفئة:</h6>
                            <span class="badge bg-secondary">{{ auction.category.name_ar }}</span>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">البائع:</h6>
                            <strong>{{ auction.seller.full_name }}</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-muted">وصف المنتج:</h6>
                        <p class="lead">{{ auction.description }}</p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-muted">السعر الابتدائي:</h6>
                            <span class="h5 text-info">${{ "%.2f"|format(auction.starting_price) }}</span>
                        </div>
                        {% if auction.reserve_price %}
                        <div class="col-md-6">
                            <h6 class="text-muted">السعر الاحتياطي:</h6>
                            <span class="h5 text-warning">${{ "%.2f"|format(auction.reserve_price) }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-muted">تاريخ البداية:</h6>
                            <span>{{ auction.start_time.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">تاريخ النهاية:</h6>
                            <span>{{ auction.end_time.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bidding History -->
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>تاريخ المزايدات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_bids %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المزايد</th>
                                <th>المبلغ</th>
                                <th>الوقت</th>
                            </tr>
                        </thead>
                        <tbody id="bids-table">
                            {% for bid in recent_bids %}
                            <tr>
                                <td>{{ bid.bidder.username }}</td>
                                <td class="fw-bold text-success">${{ "%.2f"|format(bid.amount) }}</td>
                                <td>{{ bid.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مزايدات بعد</h5>
                    <p class="text-muted">كن أول من يزايد على هذا المنتج!</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Bidding Panel -->
    <div class="col-lg-4">
        <div class="card shadow sticky-top" style="top: 20px;">
            <div class="card-header bg-success text-white text-center">
                <h5 class="mb-0">
                    <i class="fas fa-gavel me-2"></i>معلومات المزاد
                </h5>
            </div>
            <div class="card-body">
                <!-- Current Price -->
                <div class="text-center mb-4">
                    <h6 class="text-muted">السعر الحالي</h6>
                    <h2 class="text-success fw-bold" id="current-price">${{ "%.2f"|format(auction.current_price) }}</h2>
                </div>
                
                <!-- Time Remaining -->
                <div class="text-center mb-4">
                    <h6 class="text-muted">الوقت المتبقي</h6>
                    <div class="h4 text-warning" data-end-time="{{ auction.end_time.isoformat() }}">
                        <i class="fas fa-clock me-2"></i>
                        <span id="countdown-timer"></span>
                    </div>
                </div>
                
                <!-- Auction Status -->
                <div class="text-center mb-4">
                    {% if auction.is_active %}
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-play me-1"></i>مزاد نشط
                    </span>
                    {% else %}
                    <span class="badge bg-danger fs-6">
                        <i class="fas fa-stop me-1"></i>مزاد منتهي
                    </span>
                    {% endif %}
                </div>
                
                <!-- Bidding Form -->
                {% if current_user.is_authenticated and auction.is_active and auction.seller_id != current_user.id %}
                <div class="bidding-form">
                    <form id="bid-form">
                        {{ bid_form.hidden_tag() }}
                        <div class="mb-3">
                            {{ bid_form.amount.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ bid_form.amount(class="form-control", id="bid-amount") }}
                            </div>
                            <div class="form-text">
                                الحد الأدنى: ${{ "%.2f"|format(auction.current_price + 1) }}
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-gavel me-2"></i>قدم مزايدة
                            </button>
                        </div>
                    </form>
                </div>
                {% elif not current_user.is_authenticated %}
                <div class="text-center">
                    <p class="text-muted mb-3">يجب تسجيل الدخول للمزايدة</p>
                    <a href="{{ url_for('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
                {% elif auction.seller_id == current_user.id %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    هذا مزادك الخاص
                </div>
                {% else %}
                <div class="alert alert-warning text-center">
                    <i class="fas fa-clock me-2"></i>
                    المزاد غير نشط
                </div>
                {% endif %}
                
                <!-- Auction Stats -->
                <div class="auction-stats mt-4">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <h6 class="text-muted">عدد المزايدات</h6>
                                <span class="h5 text-primary" id="bid-count">{{ auction.bids|length }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h6 class="text-muted">المشاهدات</h6>
                                <span class="h5 text-info">{{ range(50, 200) | random }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Seller Info -->
        <div class="card shadow mt-4">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>معلومات البائع
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-user-circle fa-3x text-muted mb-2"></i>
                    <h6>{{ auction.seller.full_name }}</h6>
                    <p class="text-muted">@{{ auction.seller.username }}</p>
                    <small class="text-muted">عضو منذ {{ auction.seller.created_at.strftime('%Y') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Socket.IO connection
const socket = io();
const auctionId = {{ auction.id }};

// Join auction room
socket.emit('join_auction', {auction_id: auctionId});

// Listen for new bids
socket.on('new_bid', function(data) {
    if (data.auction_id === auctionId) {
        // Update current price
        document.getElementById('current-price').textContent = '$' + data.amount.toFixed(2);
        
        // Update bid count
        const bidCount = document.getElementById('bid-count');
        bidCount.textContent = parseInt(bidCount.textContent) + 1;
        
        // Add new bid to table
        const bidsTable = document.getElementById('bids-table');
        if (bidsTable) {
            const newRow = bidsTable.insertRow(0);
            newRow.innerHTML = `
                <td>${data.bidder}</td>
                <td class="fw-bold text-success">$${data.amount.toFixed(2)}</td>
                <td>${data.timestamp}</td>
            `;
        }
        
        // Update minimum bid
        const minBid = data.amount + 1;
        document.querySelector('.form-text').textContent = `الحد الأدنى: $${minBid.toFixed(2)}`;
        document.getElementById('bid-amount').setAttribute('min', minBid);
    }
});

// Countdown timer
function updateCountdown() {
    const endTime = new Date(document.querySelector('[data-end-time]').dataset.endTime);
    const now = new Date();
    const timeLeft = endTime - now;
    
    const timer = document.getElementById('countdown-timer');
    
    if (timeLeft > 0) {
        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        
        let timeString = '';
        if (days > 0) timeString += days + ' يوم ';
        if (hours > 0) timeString += hours + ' ساعة ';
        if (minutes > 0) timeString += minutes + ' دقيقة ';
        timeString += seconds + ' ثانية';
        
        timer.textContent = timeString;
    } else {
        timer.textContent = 'انتهى المزاد';
        timer.parentElement.classList.remove('text-warning');
        timer.parentElement.classList.add('text-danger');
        
        // Disable bidding form
        const bidForm = document.getElementById('bid-form');
        if (bidForm) {
            bidForm.style.display = 'none';
        }
    }
}

// Update countdown every second
updateCountdown();
setInterval(updateCountdown, 1000);

// Handle bid form submission
document.getElementById('bid-form')?.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(`/place_bid/{{ auction.id }}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Clear form
            document.getElementById('bid-amount').value = '';
            
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show mt-3';
            alert.innerHTML = `
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            this.appendChild(alert);
            
            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                alert.remove();
            }, 3000);
        } else {
            // Show error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show mt-3';
            alert.innerHTML = `
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            this.appendChild(alert);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
});
</script>
{% endblock %}
