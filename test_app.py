#!/usr/bin/env python3
"""
Simple test script for the Electronic Auction System
Tests basic functionality and API endpoints
"""

import requests
import json
from datetime import datetime

def test_homepage():
    """Test if the homepage loads correctly"""
    try:
        response = requests.get('http://localhost:5000')
        if response.status_code == 200:
            print("✅ Homepage loads successfully")
            if 'مزادات إلكترونية' in response.text:
                print("✅ Arabic content detected")
            else:
                print("⚠️  Arabic content not found")
            return True
        else:
            print(f"❌ Homepage failed with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the app is running.")
        return False

def test_login_page():
    """Test if the login page loads correctly"""
    try:
        response = requests.get('http://localhost:5000/login')
        if response.status_code == 200:
            print("✅ Login page loads successfully")
            return True
        else:
            print(f"❌ Login page failed with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        return False

def test_register_page():
    """Test if the registration page loads correctly"""
    try:
        response = requests.get('http://localhost:5000/register')
        if response.status_code == 200:
            print("✅ Registration page loads successfully")
            return True
        else:
            print(f"❌ Registration page failed with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        return False

def test_api_endpoints():
    """Test various API endpoints"""
    endpoints = [
        ('/', 'Homepage'),
        ('/login', 'Login page'),
        ('/register', 'Registration page'),
    ]
    
    results = []
    for endpoint, name in endpoints:
        try:
            response = requests.get(f'http://localhost:5000{endpoint}')
            if response.status_code == 200:
                print(f"✅ {name} - OK")
                results.append(True)
            else:
                print(f"❌ {name} - Failed ({response.status_code})")
                results.append(False)
        except requests.exceptions.ConnectionError:
            print(f"❌ {name} - Connection failed")
            results.append(False)
    
    return all(results)

def main():
    print("🧪 Testing Electronic Auction System")
    print("=" * 50)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test basic connectivity
    print("📡 Testing server connectivity...")
    if not test_homepage():
        print("\n❌ Server is not running or not accessible")
        print("💡 Make sure to run: python run.py")
        return
    
    print("\n🌐 Testing page accessibility...")
    test_login_page()
    test_register_page()
    
    print("\n🔗 Testing API endpoints...")
    if test_api_endpoints():
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed")
    
    print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n📋 Manual testing suggestions:")
    print("   1. Open http://localhost:5000 in your browser")
    print("   2. Try registering a new account")
    print("   3. Login with sample accounts")
    print("   4. Create a new auction (as seller)")
    print("   5. Place bids on auctions (as buyer)")

if __name__ == '__main__':
    main()
