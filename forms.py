from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, PasswordField, TextAreaField, FloatField, SelectField, DateTimeField, BooleanField, MultipleFileField, IntegerField
from wtforms.validators import DataRequired, Email, Length, EqualTo, NumberRange, ValidationError, Optional
from wtforms.widgets import TextArea, DateTimeLocalInput
from models import User, Category
from datetime import datetime, timedelta

class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم / Username', validators=[DataRequired()])
    password = PasswordField('كلمة المرور / Password', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني / Remember Me')

class RegistrationForm(FlaskForm):
    username = StringField('اسم المستخدم / Username', 
                          validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('البريد الإلكتروني / Email', 
                       validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل / Full Name', 
                           validators=[DataRequired(), Length(min=2, max=100)])
    phone = StringField('رقم الهاتف / Phone Number', 
                       validators=[Length(max=20)])
    password = PasswordField('كلمة المرور / Password', 
                            validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('تأكيد كلمة المرور / Confirm Password', 
                             validators=[DataRequired(), EqualTo('password')])
    role = SelectField('نوع الحساب / Account Type', 
                      choices=[('buyer', 'مشتري / Buyer'), ('seller', 'بائع / Seller')],
                      default='buyer')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر / Username already taken. Please choose another.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل / Email already registered.')

class AuctionForm(FlaskForm):
    title = StringField('عنوان المزاد / Auction Title',
                       validators=[DataRequired(), Length(min=5, max=200)])
    description = TextAreaField('وصف المنتج / Product Description',
                               validators=[DataRequired(), Length(min=10, max=1000)],
                               widget=TextArea())
    category_id = SelectField('الفئة / Category',
                             validators=[DataRequired()],
                             coerce=int)
    starting_price = FloatField('السعر الابتدائي / Starting Price',
                               validators=[DataRequired(), NumberRange(min=0.01)])
    reserve_price = FloatField('السعر الاحتياطي / Reserve Price (اختياري / Optional)',
                              validators=[Optional(), NumberRange(min=0.01)])

    # New fields for start time and duration
    start_time = StringField('تاريخ ووقت بداية المزاد / Auction Start Date & Time',
                            validators=[DataRequired()],
                            render_kw={
                                'type': 'datetime-local',
                                'class': 'form-control',
                                'id': 'start-time-input',
                                'step': '60',  # 1 minute steps
                                'pattern': '[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}'
                            })

    duration_type = SelectField('نوع المدة / Duration Type',
                               choices=[
                                   ('minutes', 'دقائق / Minutes'),
                                   ('hours', 'ساعات / Hours'),
                                   ('days', 'أيام / Days')
                               ],
                               default='days',
                               validators=[DataRequired()])

    duration_value = IntegerField('قيمة المدة / Duration Value',
                                 validators=[DataRequired(), NumberRange(min=1)])

    # Quick duration presets
    quick_duration = SelectField('مدة سريعة (اختياري) / Quick Duration (Optional)',
                               choices=[
                                   ('', 'اختر مدة مخصصة / Custom Duration'),
                                   ('5_minutes', '5 دقائق / 5 Minutes'),
                                   ('15_minutes', '15 دقيقة / 15 Minutes'),
                                   ('30_minutes', '30 دقيقة / 30 Minutes'),
                                   ('1_hour', 'ساعة واحدة / 1 Hour'),
                                   ('2_hours', 'ساعتان / 2 Hours'),
                                   ('6_hours', '6 ساعات / 6 Hours'),
                                   ('12_hours', '12 ساعة / 12 Hours'),
                                   ('1_day', 'يوم واحد / 1 Day'),
                                   ('3_days', '3 أيام / 3 Days'),
                                   ('7_days', 'أسبوع / 1 Week'),
                                   ('14_days', 'أسبوعين / 2 Weeks'),
                                   ('30_days', 'شهر / 1 Month')
                               ],
                               default='')

    images = MultipleFileField('صور المنتج / Product Images',
                              validators=[FileAllowed(['jpg', 'jpeg', 'png', 'gif'],
                                        'يُسمح فقط بملفات الصور (JPG, PNG, GIF) / Only image files allowed (JPG, PNG, GIF)')],
                              render_kw={'multiple': True, 'accept': 'image/*'})
    
    def __init__(self, *args, **kwargs):
        super(AuctionForm, self).__init__(*args, **kwargs)
        self.category_id.choices = [(c.id, f"{c.name_ar} / {c.name_en}")
                                   for c in Category.query.all()]

        # Set default start time to current time (only if no data provided)
        # This allows users to create auctions that start immediately
        if not self.start_time.data and not kwargs.get('data'):
            default_start = datetime.now()
            # Round to nearest minute to avoid seconds
            default_start = default_start.replace(second=0, microsecond=0)
            # Format for HTML5 datetime-local input
            self.start_time.data = default_start.strftime('%Y-%m-%dT%H:%M')

        # Set default duration
        if not self.duration_type.data:
            self.duration_type.data = 'days'
        if not self.duration_value.data:
            self.duration_value.data = 7

    def validate_start_time(self, start_time):
        if start_time.data:
            try:
                # Parse the datetime string from HTML5 datetime-local input
                start_datetime = datetime.strptime(start_time.data, '%Y-%m-%dT%H:%M')

                # Get current time
                now = datetime.now()

                # Allow start time to be current time or future (no minimum delay required)
                # This allows creating auctions that start immediately or at the same time
                if start_datetime < now - timedelta(minutes=5):
                    raise ValidationError('وقت بداية المزاد لا يمكن أن يكون في الماضي البعيد (أكثر من 5 دقائق) / Auction start time cannot be too far in the past (more than 5 minutes)')

                # Start time cannot be more than 30 days in the future
                max_start_time = now + timedelta(days=30)
                if start_datetime > max_start_time:
                    raise ValidationError('وقت بداية المزاد لا يمكن أن يكون أكثر من 30 يوماً في المستقبل / Auction start time cannot be more than 30 days in the future')

            except ValueError:
                raise ValidationError('تنسيق التاريخ والوقت غير صحيح. يرجى استخدام التنسيق الصحيح / Invalid date and time format. Please use the correct format')

    def validate_duration_value(self, duration_value):
        if duration_value.data and self.duration_type.data:
            duration_type = self.duration_type.data
            value = duration_value.data

            # Validate minimum durations
            if duration_type == 'minutes' and value < 5:
                raise ValidationError('الحد الأدنى للمدة بالدقائق هو 5 دقائق / Minimum duration in minutes is 5 minutes')
            elif duration_type == 'hours' and value < 1:
                raise ValidationError('الحد الأدنى للمدة بالساعات هو ساعة واحدة / Minimum duration in hours is 1 hour')
            elif duration_type == 'days' and value < 1:
                raise ValidationError('الحد الأدنى للمدة بالأيام هو يوم واحد / Minimum duration in days is 1 day')

            # Validate maximum durations
            if duration_type == 'minutes' and value > 1440:  # 24 hours
                raise ValidationError('الحد الأقصى للمدة بالدقائق هو 1440 دقيقة (24 ساعة) / Maximum duration in minutes is 1440 minutes (24 hours)')
            elif duration_type == 'hours' and value > 720:  # 30 days
                raise ValidationError('الحد الأقصى للمدة بالساعات هو 720 ساعة (30 يوم) / Maximum duration in hours is 720 hours (30 days)')
            elif duration_type == 'days' and value > 30:
                raise ValidationError('الحد الأقصى للمدة بالأيام هو 30 يوم / Maximum duration in days is 30 days')

    def validate_reserve_price(self, reserve_price):
        if reserve_price.data and self.starting_price.data:
            if reserve_price.data < self.starting_price.data:
                raise ValidationError('السعر الاحتياطي يجب أن يكون أكبر من أو يساوي السعر الابتدائي / Reserve price must be greater than or equal to starting price.')

    def get_start_datetime(self):
        """Convert start_time string to datetime object"""
        if not self.start_time.data:
            return None

        try:
            return datetime.strptime(self.start_time.data, '%Y-%m-%dT%H:%M')
        except ValueError:
            return None

    def get_end_time(self):
        """Calculate end time based on start time and duration"""
        if not self.start_time.data or not self.duration_type.data or not self.duration_value.data:
            return None

        start_time = self.get_start_datetime()
        if not start_time:
            return None

        duration_type = self.duration_type.data
        duration_value = self.duration_value.data

        if duration_type == 'minutes':
            return start_time + timedelta(minutes=duration_value)
        elif duration_type == 'hours':
            return start_time + timedelta(hours=duration_value)
        elif duration_type == 'days':
            return start_time + timedelta(days=duration_value)

        return None

    def apply_quick_duration(self):
        """Apply quick duration preset if selected"""
        if not self.quick_duration.data:
            return

        quick_duration_map = {
            '5_minutes': ('minutes', 5),
            '15_minutes': ('minutes', 15),
            '30_minutes': ('minutes', 30),
            '1_hour': ('hours', 1),
            '2_hours': ('hours', 2),
            '6_hours': ('hours', 6),
            '12_hours': ('hours', 12),
            '1_day': ('days', 1),
            '3_days': ('days', 3),
            '7_days': ('days', 7),
            '14_days': ('days', 14),
            '30_days': ('days', 30)
        }

        if self.quick_duration.data in quick_duration_map:
            duration_type, duration_value = quick_duration_map[self.quick_duration.data]
            self.duration_type.data = duration_type
            self.duration_value.data = duration_value

class BidForm(FlaskForm):
    amount = FloatField('مبلغ المزايدة / Bid Amount', 
                       validators=[DataRequired(), NumberRange(min=0.01)])
    
    def __init__(self, auction=None, *args, **kwargs):
        super(BidForm, self).__init__(*args, **kwargs)
        self.auction = auction
    
    def validate_amount(self, amount):
        if self.auction:
            min_bid = self.auction.current_price + 1.0  # Minimum increment
            if amount.data <= self.auction.current_price:
                raise ValidationError(f'يجب أن تكون المزايدة أكبر من السعر الحالي ({self.auction.current_price}) / Bid must be higher than current price ({self.auction.current_price})')
            if amount.data < min_bid:
                raise ValidationError(f'الحد الأدنى للمزايدة هو {min_bid} / Minimum bid is {min_bid}')

def safe_int_coerce(value):
    """Safely coerce value to int, return None for empty strings"""
    if value == '' or value is None:
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        return None

class SearchForm(FlaskForm):
    query = StringField('البحث / Search', validators=[Length(max=100)])
    category_id = SelectField('الفئة / Category', coerce=safe_int_coerce)
    min_price = FloatField('السعر الأدنى / Min Price', validators=[NumberRange(min=0)])
    max_price = FloatField('السعر الأعلى / Max Price', validators=[NumberRange(min=0)])
    status = SelectField('حالة المزاد / Status',
                        choices=[
                            ('', 'الكل / All'),
                            ('active', 'نشط / Active'),
                            ('ended', 'منتهي / Ended')
                        ])

    def __init__(self, *args, **kwargs):
        super(SearchForm, self).__init__(*args, **kwargs)
        self.category_id.choices = [('', 'جميع الفئات / All Categories')] + \
                                  [(c.id, f"{c.name_ar} / {c.name_en}")
                                   for c in Category.query.all()]
    
    def validate_max_price(self, max_price):
        if max_price.data and self.min_price.data:
            if max_price.data < self.min_price.data:
                raise ValidationError('السعر الأعلى يجب أن يكون أكبر من السعر الأدنى / Max price must be greater than min price')

class ProfileForm(FlaskForm):
    full_name = StringField('الاسم الكامل / Full Name', 
                           validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('البريد الإلكتروني / Email', 
                       validators=[DataRequired(), Email()])
    phone = StringField('رقم الهاتف / Phone Number', 
                       validators=[Length(max=20)])
    
    def __init__(self, original_email, *args, **kwargs):
        super(ProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
    
    def validate_email(self, email):
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('البريد الإلكتروني مستخدم بالفعل / Email already registered.')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية / Current Password',
                                    validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة / New Password',
                                validators=[DataRequired(), Length(min=6)])
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة / Confirm New Password',
                                 validators=[DataRequired(), EqualTo('new_password')])

class CategoryForm(FlaskForm):
    name_ar = StringField('الاسم بالعربية / Arabic Name',
                         validators=[DataRequired(), Length(min=2, max=100)])
    name_en = StringField('الاسم بالإنجليزية / English Name',
                         validators=[DataRequired(), Length(min=2, max=100)])
    description_ar = TextAreaField('الوصف بالعربية / Arabic Description',
                                  validators=[Optional(), Length(max=500)])
    description_en = TextAreaField('الوصف بالإنجليزية / English Description',
                                  validators=[Optional(), Length(max=500)])

    def __init__(self, original_name_ar=None, original_name_en=None, *args, **kwargs):
        super(CategoryForm, self).__init__(*args, **kwargs)
        self.original_name_ar = original_name_ar
        self.original_name_en = original_name_en

    def validate_name_ar(self, name_ar):
        if name_ar.data != self.original_name_ar:
            category = Category.query.filter_by(name_ar=name_ar.data).first()
            if category:
                raise ValidationError('اسم الفئة بالعربية موجود بالفعل / Arabic category name already exists')

    def validate_name_en(self, name_en):
        if name_en.data != self.original_name_en:
            category = Category.query.filter_by(name_en=name_en.data).first()
            if category:
                raise ValidationError('اسم الفئة بالإنجليزية موجود بالفعل / English category name already exists')

class DeleteCategoryForm(FlaskForm):
    confirm = BooleanField('أؤكد حذف هذه الفئة / I confirm deletion of this category',
                          validators=[DataRequired()])
    replacement_category_id = SelectField('الفئة البديلة / Replacement Category',
                                        coerce=int,
                                        validators=[Optional()])

    def __init__(self, category_to_delete=None, *args, **kwargs):
        super(DeleteCategoryForm, self).__init__(*args, **kwargs)
        self.category_to_delete = category_to_delete

        # Populate replacement categories (exclude the one being deleted)
        if category_to_delete:
            choices = [(0, 'لا توجد فئة بديلة / No replacement category')]
            categories = Category.query.filter(Category.id != category_to_delete.id).all()
            choices.extend([(c.id, f"{c.name_ar} / {c.name_en}") for c in categories])
            self.replacement_category_id.choices = choices

class SystemSettingsForm(FlaskForm):
    # General Settings
    site_name = StringField('اسم الموقع / Site Name',
                           validators=[DataRequired(), Length(min=2, max=100)],
                           render_kw={'placeholder': 'نظام المزادات الإلكترونية'})

    site_description = TextAreaField('وصف الموقع / Site Description',
                                   validators=[Optional(), Length(max=500)],
                                   render_kw={'rows': 3, 'placeholder': 'منصة مزادات إلكترونية متقدمة'})

    contact_email = StringField('البريد الإلكتروني للتواصل / Contact Email',
                               validators=[Optional(), Email()],
                               render_kw={'placeholder': '<EMAIL>'})

    support_phone = StringField('رقم الدعم الفني / Support Phone',
                               validators=[Optional(), Length(max=20)],
                               render_kw={'placeholder': '+966 50 123 4567'})

    # Auction Settings
    min_auction_duration = IntegerField('الحد الأدنى لمدة المزاد (دقائق) / Min Auction Duration (minutes)',
                                       validators=[DataRequired(), NumberRange(min=5, max=10080)],
                                       default=5)

    max_auction_duration = IntegerField('الحد الأقصى لمدة المزاد (دقائق) / Max Auction Duration (minutes)',
                                       validators=[DataRequired(), NumberRange(min=60, max=43200)],
                                       default=10080)

    min_bid_increment = FloatField('الحد الأدنى لزيادة المزايدة / Min Bid Increment',
                                  validators=[DataRequired(), NumberRange(min=0.01, max=1000)],
                                  default=1.0)

    auction_extension_time = IntegerField('وقت تمديد المزاد (ثواني) / Auction Extension Time (seconds)',
                                         validators=[DataRequired(), NumberRange(min=0, max=600)],
                                         default=300)

    # Display Settings
    auctions_per_page = IntegerField('عدد المزادات في الصفحة / Auctions Per Page',
                                    validators=[DataRequired(), NumberRange(min=5, max=100)],
                                    default=12)

    show_ended_auctions = BooleanField('عرض المزادات المنتهية / Show Ended Auctions',
                                      default=True)

    enable_real_time_updates = BooleanField('تفعيل التحديثات المباشرة / Enable Real-time Updates',
                                           default=True)

    # Top Bar Settings
    enable_top_bar = BooleanField('تفعيل الشريط العلوي / Enable Top Bar',
                                 default=True)

    top_bar_speed = IntegerField('سرعة الشريط العلوي (ثانية) / Top Bar Speed (seconds)',
                                validators=[DataRequired(), NumberRange(min=1, max=60)],
                                default=5)

    top_bar_auctions_count = IntegerField('عدد المزادات في الشريط العلوي / Top Bar Auctions Count',
                                         validators=[DataRequired(), NumberRange(min=3, max=20)],
                                         default=10)

    # Security Settings
    max_login_attempts = IntegerField('الحد الأقصى لمحاولات تسجيل الدخول / Max Login Attempts',
                                     validators=[DataRequired(), NumberRange(min=3, max=20)],
                                     default=5)

    session_timeout = IntegerField('انتهاء الجلسة (دقائق) / Session Timeout (minutes)',
                                  validators=[DataRequired(), NumberRange(min=15, max=1440)],
                                  default=60)

    # Email Settings
    enable_email_notifications = BooleanField('تفعيل إشعارات البريد الإلكتروني / Enable Email Notifications',
                                             default=True)

    # File Upload Settings
    max_image_size = IntegerField('الحد الأقصى لحجم الصورة (MB) / Max Image Size (MB)',
                                 validators=[DataRequired(), NumberRange(min=1, max=50)],
                                 default=5)

    allowed_image_types = StringField('أنواع الصور المسموحة / Allowed Image Types',
                                     validators=[DataRequired()],
                                     default='jpg,jpeg,png,gif',
                                     render_kw={'placeholder': 'jpg,jpeg,png,gif'})

    submit = SubmitField('حفظ الإعدادات / Save Settings')
