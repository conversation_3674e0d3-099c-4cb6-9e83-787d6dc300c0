from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, TextAreaField, FloatField, SelectField, DateTimeField, BooleanField
from wtforms.validators import DataRequired, Email, Length, EqualTo, NumberRange, ValidationError
from wtforms.widgets import TextArea
from models import User, Category
from datetime import datetime, timedelta

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('اسم المستخدم / Username', validators=[DataRequired()])
    password = PasswordField('كلمة المرور / Password', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني / Remember Me')

class RegistrationForm(FlaskForm):
    username = StringField('اسم المستخدم / Username', 
                          validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('البريد الإلكتروني / Email', 
                       validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل / Full Name', 
                           validators=[DataRequired(), Length(min=2, max=100)])
    phone = StringField('رقم الهاتف / Phone Number', 
                       validators=[Length(max=20)])
    password = PasswordField('كلمة المرور / Password', 
                            validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('تأكيد كلمة المرور / Confirm Password', 
                             validators=[DataRequired(), EqualTo('password')])
    role = SelectField('نوع الحساب / Account Type', 
                      choices=[('buyer', 'مشتري / Buyer'), ('seller', 'بائع / Seller')],
                      default='buyer')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر / Username already taken. Please choose another.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل / Email already registered.')

class AuctionForm(FlaskForm):
    title = StringField('عنوان المزاد / Auction Title', 
                       validators=[DataRequired(), Length(min=5, max=200)])
    description = TextAreaField('وصف المنتج / Product Description', 
                               validators=[DataRequired(), Length(min=10, max=1000)],
                               widget=TextArea())
    category_id = SelectField('الفئة / Category', 
                             validators=[DataRequired()], 
                             coerce=int)
    starting_price = FloatField('السعر الابتدائي / Starting Price', 
                               validators=[DataRequired(), NumberRange(min=0.01)])
    reserve_price = FloatField('السعر الاحتياطي / Reserve Price (اختياري / Optional)', 
                              validators=[NumberRange(min=0.01)])
    duration_days = SelectField('مدة المزاد / Auction Duration', 
                               choices=[
                                   (1, 'يوم واحد / 1 Day'),
                                   (3, '3 أيام / 3 Days'),
                                   (7, 'أسبوع / 1 Week'),
                                   (14, 'أسبوعين / 2 Weeks'),
                                   (30, 'شهر / 1 Month')
                               ],
                               default=7,
                               coerce=int)
    
    def __init__(self, *args, **kwargs):
        super(AuctionForm, self).__init__(*args, **kwargs)
        self.category_id.choices = [(c.id, f"{c.name_ar} / {c.name_en}") 
                                   for c in Category.query.all()]
    
    def validate_reserve_price(self, reserve_price):
        if reserve_price.data and self.starting_price.data:
            if reserve_price.data < self.starting_price.data:
                raise ValidationError('السعر الاحتياطي يجب أن يكون أكبر من أو يساوي السعر الابتدائي / Reserve price must be greater than or equal to starting price.')

class BidForm(FlaskForm):
    amount = FloatField('مبلغ المزايدة / Bid Amount', 
                       validators=[DataRequired(), NumberRange(min=0.01)])
    
    def __init__(self, auction=None, *args, **kwargs):
        super(BidForm, self).__init__(*args, **kwargs)
        self.auction = auction
    
    def validate_amount(self, amount):
        if self.auction:
            min_bid = self.auction.current_price + 1.0  # Minimum increment
            if amount.data <= self.auction.current_price:
                raise ValidationError(f'يجب أن تكون المزايدة أكبر من السعر الحالي ({self.auction.current_price}) / Bid must be higher than current price ({self.auction.current_price})')
            if amount.data < min_bid:
                raise ValidationError(f'الحد الأدنى للمزايدة هو {min_bid} / Minimum bid is {min_bid}')

def safe_int_coerce(value):
    """Safely coerce value to int, return None for empty strings"""
    if value == '' or value is None:
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        return None

class SearchForm(FlaskForm):
    query = StringField('البحث / Search', validators=[Length(max=100)])
    category_id = SelectField('الفئة / Category', coerce=safe_int_coerce)
    min_price = FloatField('السعر الأدنى / Min Price', validators=[NumberRange(min=0)])
    max_price = FloatField('السعر الأعلى / Max Price', validators=[NumberRange(min=0)])
    status = SelectField('حالة المزاد / Status',
                        choices=[
                            ('', 'الكل / All'),
                            ('active', 'نشط / Active'),
                            ('ended', 'منتهي / Ended')
                        ])

    def __init__(self, *args, **kwargs):
        super(SearchForm, self).__init__(*args, **kwargs)
        self.category_id.choices = [('', 'جميع الفئات / All Categories')] + \
                                  [(c.id, f"{c.name_ar} / {c.name_en}")
                                   for c in Category.query.all()]
    
    def validate_max_price(self, max_price):
        if max_price.data and self.min_price.data:
            if max_price.data < self.min_price.data:
                raise ValidationError('السعر الأعلى يجب أن يكون أكبر من السعر الأدنى / Max price must be greater than min price')

class ProfileForm(FlaskForm):
    full_name = StringField('الاسم الكامل / Full Name', 
                           validators=[DataRequired(), Length(min=2, max=100)])
    email = StringField('البريد الإلكتروني / Email', 
                       validators=[DataRequired(), Email()])
    phone = StringField('رقم الهاتف / Phone Number', 
                       validators=[Length(max=20)])
    
    def __init__(self, original_email, *args, **kwargs):
        super(ProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
    
    def validate_email(self, email):
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('البريد الإلكتروني مستخدم بالفعل / Email already registered.')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية / Current Password', 
                                    validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة / New Password', 
                                validators=[DataRequired(), Length(min=6)])
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة / Confirm New Password', 
                                 validators=[DataRequired(), EqualTo('new_password')])
