{% extends "base.html" %}
{% from 'macros/auction_status.html' import auction_status_badge %}

{% block title %}مزاداتي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-gavel me-2"></i>مزاداتي</h2>
                <p class="text-muted">إدارة جميع المزادات التي قمت بإنشائها</p>
            </div>
            <div>
                <a href="{{ url_for('create_auction') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء مزاد جديد
                </a>
            </div>
        </div>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('profile') }}">الملف الشخصي</a></li>
                <li class="breadcrumb-item active">مزاداتي</li>
            </ol>
        </nav>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-list fa-2x mb-2"></i>
                        <h4>{{ auctions|length }}</h4>
                        <small>إجمالي المزادات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-play fa-2x mb-2"></i>
                        <h4>{{ auctions|selectattr('status.value', 'equalto', 'active')|list|length }}</h4>
                        <small>مزادات نشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check fa-2x mb-2"></i>
                        <h4>{{ auctions|selectattr('status.value', 'equalto', 'ended')|list|length }}</h4>
                        <small>مزادات منتهية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>${{ "%.0f"|format(auctions|selectattr('status.value', 'equalto', 'ended')|selectattr('winner_id')|map(attribute='current_price')|sum) }}</h4>
                        <small>إجمالي المبيعات</small>
                    </div>
                </div>
            </div>
        </div>
        
        {% if auctions %}
        <!-- Pending Auctions Alert -->
        {% set pending_auctions = auctions|selectattr('status.value', 'equalto', 'pending')|list %}
        {% if pending_auctions %}
        <div class="alert alert-info alert-dismissible fade show mb-4">
            <i class="fas fa-info-circle me-2"></i>
            <strong>لديك {{ pending_auctions|length }} مزاد معلق</strong>
            {% if pending_auctions|length == 1 %}
                سيبدأ قريباً
            {% else %}
                ستبدأ قريباً
            {% endif %}
            <br>
            <small>المزادات المعلقة ستبدأ تلقائياً في الوقت المحدد. لا حاجة لأي إجراء من جانبك.</small>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}

        <!-- Auctions Table -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>جميع المزادات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الصورة</th>
                                <th>العنوان</th>
                                <th>الفئة</th>
                                <th>السعر الابتدائي</th>
                                <th>السعر الحالي</th>
                                <th>عدد المزايدات</th>
                                <th>الحالة</th>
                                <th>الفائز</th>
                                <th>التوقيت</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for auction in auctions %}
                            <tr data-auction-id="{{ auction.id }}">
                                <td>
                                    {% if auction.images %}
                                    <img src="{{ url_for('static', filename='uploads/auctions/thumb_' + auction.images[0].filename) }}" 
                                         alt="{{ auction.title }}" class="auction-thumb">
                                    {% else %}
                                    <div class="auction-thumb-placeholder">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ auction.title }}</strong>
                                    <br><small class="text-muted">{{ auction.description[:50] }}...</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ auction.category.name_ar }}</span>
                                </td>
                                <td class="fw-bold">${{ "%.2f"|format(auction.starting_price) }}</td>
                                <td class="fw-bold text-success">${{ "%.2f"|format(auction.current_price) }}</td>
                                <td>
                                    <span class="badge bg-info">{{ auction.bids|length }}</span>
                                </td>
                                <td>
                                    {{ auction_status_badge(auction) }}
                                </td>
                                <td>
                                    {% if auction.status.value == 'ended' %}
                                        {% if auction.winner_id %}
                                        <div class="winner-info">
                                            <i class="fas fa-trophy text-warning me-1"></i>
                                            <strong class="text-success">{{ auction.winner.full_name }}</strong>
                                            <br>
                                            <small class="text-muted">@{{ auction.winner.username }}</small>
                                        </div>
                                        {% else %}
                                        <small class="text-muted">
                                            <i class="fas fa-times-circle me-1"></i>لا يوجد فائز
                                        </small>
                                        {% endif %}
                                    {% else %}
                                        <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if auction.status.value == 'pending' %}
                                        <small class="text-warning">
                                            <i class="fas fa-clock me-1"></i>يبدأ في:
                                        </small>
                                        <br>
                                        <small>{{ auction.start_time.strftime('%Y-%m-%d') }}</small>
                                        <br>
                                        <small class="text-muted">{{ auction.start_time.strftime('%H:%M') }}</small>
                                        <br>
                                        <small class="text-info">{{ auction.start_time | time_until_start }}</small>
                                    {% else %}
                                        <small>{{ auction.end_time.strftime('%Y-%m-%d') }}</small>
                                        <br><small class="text-muted">{{ auction.end_time.strftime('%H:%M') }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm">
                                        <!-- View Details -->
                                        <a href="{{ url_for('auction_detail', id=auction.id) }}"
                                           class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <!-- Edit Button -->
                                        {% if auction.status.value != 'ended' %}
                                            {% if auction.status.value == 'pending' or (auction.status.value == 'active' and auction.bids|length == 0) %}
                                                <a href="{{ url_for('edit_auction', id=auction.id) }}"
                                                   class="btn btn-outline-warning btn-sm" title="تعديل المزاد">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% else %}
                                                <button class="btn btn-outline-warning btn-sm"
                                                        disabled
                                                        title="لا يمكن تعديل مزاد نشط يحتوي على مزايدات">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            {% endif %}
                                        {% endif %}

                                        <!-- Delete Button -->
                                        {% if auction.status.value != 'ended' or not auction.winner_id %}
                                            {% if auction.status.value == 'pending' or (auction.status.value == 'active' and auction.bids|length == 0) or (auction.status.value == 'ended' and not auction.winner_id) %}
                                                <a href="{{ url_for('delete_auction', id=auction.id) }}"
                                                   class="btn btn-outline-danger btn-sm" title="حذف المزاد">
                                                    <i class="fas fa-trash-alt"></i>
                                                </a>
                                            {% else %}
                                                <button class="btn btn-outline-danger btn-sm"
                                                        disabled
                                                        title="لا يمكن حذف مزاد نشط يحتوي على مزايدات">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            {% endif %}
                                        {% endif %}

                                        <!-- Additional Actions -->
                                        {% if auction.status.value == 'active' %}
                                        <button class="btn btn-outline-info btn-sm" title="إحصائيات">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        {% endif %}
                                        {% if auction.status.value == 'ended' and auction.winner_id %}
                                        <button class="btn btn-outline-success btn-sm" title="تم البيع">
                                            <i class="fas fa-check-circle"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        {% else %}
        <!-- Empty State -->
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-gavel fa-5x text-muted mb-4"></i>
                <h3 class="text-muted mb-3">لم تقم بإنشاء أي مزادات بعد</h3>
                <p class="text-muted mb-4">ابدأ في بيع منتجاتك عن طريق إنشاء مزاد جديد</p>
                <a href="{{ url_for('create_auction') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>إنشاء أول مزاد
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.auction-thumb {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #dee2e6;
}

.auction-thumb-placeholder {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.badge {
    font-size: 0.75rem;
}

.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
}

.winner-info {
    text-align: center;
}

.winner-info .fas.fa-trophy {
    font-size: 1.1em;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .auction-thumb,
    .auction-thumb-placeholder {
        width: 40px;
        height: 40px;
    }
    
    .btn-group-vertical .btn {
        padding: 4px 8px;
    }
}
</style>

<script>
// Check for and remove duplicate auction rows
document.addEventListener('DOMContentLoaded', function() {
    const auctionRows = document.querySelectorAll('tr[data-auction-id]');
    const seenIds = new Set();
    let duplicatesFound = 0;

    auctionRows.forEach(function(row) {
        const auctionId = row.getAttribute('data-auction-id');

        if (seenIds.has(auctionId)) {
            // This is a duplicate, remove it
            row.remove();
            duplicatesFound++;
            console.warn(`Removed duplicate auction row with ID: ${auctionId}`);
        } else {
            seenIds.add(auctionId);
        }
    });

    if (duplicatesFound > 0) {
        console.warn(`Found and removed ${duplicatesFound} duplicate auction rows`);

        // Show a warning to the user
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> تم العثور على ${duplicatesFound} مزاد مكرر وتم إزالته من العرض.
            <br>
            <small>إذا استمرت هذه المشكلة، يرجى الاتصال بالدعم الفني.</small>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.row');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
        }
    }
});
</script>
{% endblock %}
