#!/usr/bin/env python3
"""
Setup script for the Electronic Auction System
This script installs dependencies and initializes the database
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"   {e.stderr}")
        return False

def main():
    print("🚀 Setting up Electronic Auction System")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Initialize database
    if not run_command("python init_db.py", "Initializing database"):
        print("❌ Failed to initialize database")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Run: python run.py")
    print("   2. Open: http://localhost:5000")
    print("   3. Login with sample accounts (see README.md)")
    print("\n📚 Sample accounts:")
    print("   • Admin: admin / admin123")
    print("   • Seller: ahmed_seller / password123")
    print("   • Buyer: omar_buyer / password123")

if __name__ == '__main__':
    main()
