{% extends "base.html" %}

{% block title %}تسجيل الدخول - مزادات إلكترونية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </h4>
            </div>
            <div class="card-body p-4">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>دخول
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="mb-2">ليس لديك حساب؟</p>
                    <a href="{{ url_for('register') }}" class="btn btn-outline-success">
                        <i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد
                    </a>
                </div>
                
                <div class="text-center mt-3">
                    <a href="#" class="text-muted small">نسيت كلمة المرور؟</a>
                </div>
            </div>
        </div>
        
        <!-- Features Section -->
        <div class="mt-4">
            <div class="row text-center">
                <div class="col-4">
                    <div class="feature-item p-3">
                        <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                        <h6>آمن</h6>
                        <small class="text-muted">حماية كاملة لبياناتك</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="feature-item p-3">
                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                        <h6>سريع</h6>
                        <small class="text-muted">مزايدات فورية</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="feature-item p-3">
                        <i class="fas fa-handshake fa-2x text-warning mb-2"></i>
                        <h6>موثوق</h6>
                        <small class="text-muted">صفقات آمنة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
