{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ current_user.full_name }}{% endblock %}

{% block content %}
<div class="row">
    <!-- User Info Card -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>معلومات المستخدم
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="profile-avatar mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                
                <h4 class="mb-2">{{ current_user.full_name }}</h4>
                <p class="text-muted mb-1">@{{ current_user.username }}</p>
                
                <div class="user-role mb-3">
                    {% if current_user.role.value == 'seller' %}
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-store me-1"></i>بائع
                    </span>
                    {% elif current_user.role.value == 'buyer' %}
                    <span class="badge bg-info fs-6">
                        <i class="fas fa-shopping-cart me-1"></i>مشتري
                    </span>
                    {% elif current_user.role.value == 'admin' %}
                    <span class="badge bg-danger fs-6">
                        <i class="fas fa-crown me-1"></i>مدير
                    </span>
                    {% endif %}
                </div>
                
                <div class="user-details text-start">
                    <div class="detail-item mb-2">
                        <i class="fas fa-envelope text-muted me-2"></i>
                        <span>{{ current_user.email }}</span>
                    </div>
                    {% if current_user.phone %}
                    <div class="detail-item mb-2">
                        <i class="fas fa-phone text-muted me-2"></i>
                        <span>{{ current_user.phone }}</span>
                    </div>
                    {% endif %}
                    <div class="detail-item mb-2">
                        <i class="fas fa-calendar text-muted me-2"></i>
                        <span>عضو منذ {{ current_user.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                </div>
                
                <div class="profile-actions mt-4">
                    <a href="{{ url_for('edit_profile') }}" class="btn btn-primary btn-sm me-2">
                        <i class="fas fa-edit me-1"></i>تعديل الملف
                    </a>
                    <a href="{{ url_for('quick_password_change') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics and Activity -->
    <div class="col-lg-8">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-gavel fa-2x mb-2"></i>
                        <h4>{{ stats.total_auctions }}</h4>
                        <small>إجمالي المزادات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-play fa-2x mb-2"></i>
                        <h4>{{ stats.active_auctions }}</h4>
                        <small>مزادات نشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-hand-paper fa-2x mb-2"></i>
                        <h4>{{ stats.total_bids }}</h4>
                        <small>إجمالي المزايدات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy fa-2x mb-2"></i>
                        <h4>{{ stats.won_auctions }}</h4>
                        <small>مزادات فائزة</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity Tabs -->
        <div class="card shadow">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="activityTabs" role="tablist">
                    {% if current_user.role.value == 'seller' %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="auctions-tab" data-bs-toggle="tab" data-bs-target="#auctions" type="button" role="tab">
                            <i class="fas fa-gavel me-1"></i>مزاداتي
                        </button>
                    </li>
                    {% endif %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link {% if current_user.role.value != 'seller' %}active{% endif %}" id="bids-tab" data-bs-toggle="tab" data-bs-target="#bids" type="button" role="tab">
                            <i class="fas fa-hand-paper me-1"></i>مزايداتي
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="won-tab" data-bs-toggle="tab" data-bs-target="#won" type="button" role="tab">
                            <i class="fas fa-trophy me-1"></i>المزادات الفائزة
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="activityTabsContent">
                    <!-- My Auctions Tab -->
                    {% if current_user.role.value == 'seller' %}
                    <div class="tab-pane fade show active" id="auctions" role="tabpanel">
                        {% if user_auctions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>السعر الحالي</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for auction in user_auctions[:5] %}
                                    <tr>
                                        <td>
                                            <strong>{{ auction.title }}</strong>
                                            <br><small class="text-muted">{{ auction.category.name_ar }}</small>
                                        </td>
                                        <td class="fw-bold text-success">${{ "%.2f"|format(auction.current_price) }}</td>
                                        <td>
                                            {% if auction.status.value == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                            {% elif auction.status.value == 'ended' %}
                                            <span class="badge bg-secondary">منتهي</span>
                                            {% else %}
                                            <span class="badge bg-warning">{{ auction.status.value }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ auction.end_time.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <a href="{{ url_for('auction_detail', id=auction.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if user_auctions|length > 5 %}
                        <div class="text-center">
                            <a href="{{ url_for('my_auctions') }}" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>عرض جميع المزادات ({{ user_auctions|length }})
                            </a>
                        </div>
                        {% endif %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لم تقم بإنشاء أي مزادات بعد</h5>
                            <a href="{{ url_for('create_auction') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إنشاء مزاد جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- My Bids Tab -->
                    <div class="tab-pane fade {% if current_user.role.value != 'seller' %}show active{% endif %}" id="bids" role="tabpanel">
                        {% if user_bids %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المزاد</th>
                                        <th>مزايدتي</th>
                                        <th>السعر الحالي</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for bid in user_bids[:5] %}
                                    <tr>
                                        <td>
                                            <strong>{{ bid.auction.title }}</strong>
                                            <br><small class="text-muted">{{ bid.auction.seller.username }}</small>
                                        </td>
                                        <td class="fw-bold">${{ "%.2f"|format(bid.amount) }}</td>
                                        <td class="text-success">${{ "%.2f"|format(bid.auction.current_price) }}</td>
                                        <td>{{ bid.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            {% if bid.amount == bid.auction.current_price %}
                                            <span class="badge bg-success">أعلى مزايدة</span>
                                            {% else %}
                                            <span class="badge bg-secondary">تم تجاوزها</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if user_bids|length > 5 %}
                        <div class="text-center">
                            <a href="{{ url_for('my_bids') }}" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>عرض جميع المزايدات ({{ user_bids|length }})
                            </a>
                        </div>
                        {% endif %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-hand-paper fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لم تقم بأي مزايدات بعد</h5>
                            <a href="{{ url_for('index') }}" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>تصفح المزادات
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Won Auctions Tab -->
                    <div class="tab-pane fade" id="won" role="tabpanel">
                        {% if won_auctions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المزاد</th>
                                        <th>السعر النهائي</th>
                                        <th>البائع</th>
                                        <th>تاريخ الانتهاء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for auction in won_auctions %}
                                    <tr>
                                        <td>
                                            <strong>{{ auction.title }}</strong>
                                            <br><small class="text-muted">{{ auction.category.name_ar }}</small>
                                        </td>
                                        <td class="fw-bold text-success">${{ "%.2f"|format(auction.current_price) }}</td>
                                        <td>{{ auction.seller.full_name }}</td>
                                        <td>{{ auction.end_time.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لم تفز بأي مزادات بعد</h5>
                            <p class="text-muted">استمر في المزايدة لتحقيق أول فوز!</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
