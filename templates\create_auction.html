{% extends "base.html" %}

{% block title %}إنشاء مزاد جديد - مزادات إلكترونية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-plus me-2"></i>إنشاء مزاد جديد
                </h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات المنتج
                        </h5>
                        <hr>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اختر عنواناً واضحاً وجذاباً للمنتج</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="5") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اكتب وصفاً مفصلاً للمنتج يشمل الحالة والمواصفات</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.category_id.label(class="form-label") }}
                        {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                        {% if form.category_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.category_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fas fa-dollar-sign me-2"></i>معلومات السعر
                        </h5>
                        <hr>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.starting_price.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.starting_price(class="form-control" + (" is-invalid" if form.starting_price.errors else "")) }}
                            </div>
                            {% if form.starting_price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.starting_price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">السعر الذي سيبدأ به المزاد</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.reserve_price.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.reserve_price(class="form-control" + (" is-invalid" if form.reserve_price.errors else "")) }}
                            </div>
                            {% if form.reserve_price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.reserve_price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">الحد الأدنى للسعر المقبول (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="text-primary">
                            <i class="fas fa-clock me-2"></i>مدة المزاد
                        </h5>
                        <hr>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.duration_days.label(class="form-label") }}
                        {{ form.duration_days(class="form-select" + (" is-invalid" if form.duration_days.errors else "")) }}
                        {% if form.duration_days.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.duration_days.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اختر المدة المناسبة لمزادك</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>نصائح لمزاد ناجح:</strong>
                        <ul class="mb-0 mt-2">
                            <li>استخدم صوراً واضحة وعالية الجودة</li>
                            <li>اكتب وصفاً مفصلاً وصادقاً</li>
                            <li>حدد سعراً ابتدائياً معقولاً</li>
                            <li>اختر مدة مناسبة للمزاد</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>إنشاء المزاد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
