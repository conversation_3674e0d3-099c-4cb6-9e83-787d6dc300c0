#!/usr/bin/env python3
"""
Simple run script for the Electronic Auction System
"""

from app import create_app

if __name__ == '__main__':
    app, socketio = create_app()
    
    print("🚀 Starting Electronic Auction System...")
    print("📱 Arabic Language Support Enabled")
    print("🌐 Open your browser and go to: http://localhost:5000")
    print("👤 Sample accounts available (see README.md)")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        socketio.run(app, debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Goodbye!")
